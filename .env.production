# RatioHub Production Environment Configuration

# Supabase Configuration
VITE_SUPABASE_URL=https://rgcxrksmdkzarcdlscpk.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJnY3hya3NtZGt6YXJjZGxzY3BrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0MzE0NzQsImV4cCI6MjA1MDAwNzQ3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8

# Application Configuration
VITE_APP_ENV=production
VITE_APP_NAME=RatioHub
VITE_APP_VERSION=1.0.0

# API Configuration
VITE_API_TIMEOUT=30000

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Security Configuration
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_ONLY=true

# Build Configuration
VITE_BUILD_TIMESTAMP=__BUILD_TIMESTAMP__
