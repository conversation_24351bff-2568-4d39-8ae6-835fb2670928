@tailwind base;
@tailwind components;
@tailwind utilities;

/* RatioHub Design System - Modern Project Management UI */

@layer base {
  :root {
    /* Core brand colors based on #4F46E5 */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* RatioHub Primary Brand Color (#4F46E5) */
    --primary: 245 58% 58%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 245 58% 70%;
    --primary-dark: 245 58% 45%;

    /* Secondary colors derived from primary */
    --secondary: 245 20% 96%;
    --secondary-foreground: 245 58% 15%;
    --secondary-accent: 245 30% 88%;

    /* Status colors */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Neutral palette */
    --muted: 245 10% 97%;
    --muted-foreground: 245 16% 46%;
    --accent: 245 10% 95%;
    --accent-foreground: 245 20% 20%;

    /* Border and input */
    --border: 245 20% 90%;
    --input: 245 20% 90%;
    --ring: 245 58% 58%;

    /* Design tokens */
    --radius: 0.75rem;
    --spacing-section: 5rem;
    --spacing-component: 2rem;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-light)) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(var(--secondary-accent)) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(245 70% 65%) 50%, hsl(280 60% 70%) 100%);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 hsl(var(--primary) / 0.05);
    --shadow-md: 0 4px 6px -1px hsl(var(--primary) / 0.1), 0 2px 4px -1px hsl(var(--primary) / 0.06);
    --shadow-lg: 0 10px 15px -3px hsl(var(--primary) / 0.1), 0 4px 6px -2px hsl(var(--primary) / 0.05);
    --shadow-xl: 0 20px 25px -5px hsl(var(--primary) / 0.1), 0 10px 10px -5px hsl(var(--primary) / 0.04);
    --shadow-primary: 0 10px 30px -10px hsl(var(--primary) / 0.3);
    --shadow-glow: 0 0 40px hsl(var(--primary) / 0.15);

    /* Animations */
    --transition-base: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark mode for RatioHub */
    --background: 245 25% 8%;
    --foreground: 245 10% 95%;

    --card: 245 20% 10%;
    --card-foreground: 245 10% 95%;

    --popover: 245 20% 10%;
    --popover-foreground: 245 10% 95%;

    /* Keep primary brand color consistent */
    --primary: 245 58% 65%;
    --primary-foreground: 245 25% 8%;
    --primary-light: 245 58% 75%;
    --primary-dark: 245 58% 55%;

    --secondary: 245 20% 15%;
    --secondary-foreground: 245 10% 90%;
    --secondary-accent: 245 20% 20%;

    --success: 142 76% 45%;
    --warning: 38 92% 60%;
    --destructive: 0 84% 65%;

    --muted: 245 15% 12%;
    --muted-foreground: 245 10% 60%;
    --accent: 245 15% 15%;
    --accent-foreground: 245 10% 90%;

    --border: 245 20% 20%;
    --input: 245 20% 15%;
    --ring: 245 58% 65%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar styles for hidden scrollbars */
@layer utilities {
  .scrollbar-hidden {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
  }

  .scrollbar-hidden::-webkit-scrollbar {
    display: none; /* WebKit */
  }
}