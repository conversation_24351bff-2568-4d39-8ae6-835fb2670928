import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Download, 
  Send, 
  DollarSign,
  Calendar,
  FileText,
  Building,
  User,
  ArrowLeft
} from 'lucide-react';

interface InvoiceItem {
  id: string;
  description: string;
  hours: number;
  rate: number;
  amount: number;
}

interface Invoice {
  id: string;
  invoice_number: string;
  client_name: string;
  project_name: string;
  amount: number;
  tax_amount: number;
  total_amount: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue';
  issue_date: string;
  due_date: string;
  items: InvoiceItem[];
}

interface InvoiceDetailViewProps {
  invoice: Invoice;
  onBack: () => void;
  onDownload?: (invoiceId: string) => void;
  onSend?: (invoiceId: string) => void;
}

export const InvoiceDetailView: React.FC<InvoiceDetailViewProps> = ({
  invoice,
  onBack,
  onDownload,
  onSend
}) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusColor = (status: Invoice['status']) => {
    switch (status) {
      case 'paid': return 'default';
      case 'sent': return 'secondary';
      case 'overdue': return 'destructive';
      case 'draft': return 'outline';
      default: return 'outline';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={onBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Invoices
          </Button>
          <div>
            <h2 className="text-2xl font-bold">{invoice.invoice_number}</h2>
            <p className="text-muted-foreground">Invoice Details</p>
          </div>
        </div>
        <div className="flex gap-2">
          {onDownload && (
            <Button variant="outline" onClick={() => onDownload(invoice.id)}>
              <Download className="h-4 w-4 mr-2" />
              Download PDF
            </Button>
          )}
          {invoice.status === 'draft' && onSend && (
            <Button onClick={() => onSend(invoice.id)}>
              <Send className="h-4 w-4 mr-2" />
              Send Invoice
            </Button>
          )}
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Invoice Information
                </CardTitle>
                <Badge variant={getStatusColor(invoice.status)}>
                  {invoice.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <Building className="h-4 w-4" />
                      From
                    </h4>
                    <div className="text-sm text-muted-foreground">
                      <p className="font-medium">RatioHub Solutions</p>
                      <p>123 Business St</p>
                      <p>City, State 12345</p>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <User className="h-4 w-4" />
                      To
                    </h4>
                    <div className="text-sm text-muted-foreground">
                      <p className="font-medium">{invoice.client_name}</p>
                      <p>Project: {invoice.project_name}</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Dates
                    </h4>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <p>Issue Date: {new Date(invoice.issue_date).toLocaleDateString()}</p>
                      <p>Due Date: {new Date(invoice.due_date).toLocaleDateString()}</p>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Total Amount
                    </h4>
                    <div className="text-2xl font-bold text-primary">
                      {formatCurrency(invoice.total_amount)}
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="font-medium mb-4">Invoice Items</h4>
                <div className="space-y-3">
                  <div className="grid grid-cols-5 gap-4 text-sm font-medium text-muted-foreground border-b pb-2">
                    <div className="col-span-2">Description</div>
                    <div>Hours</div>
                    <div>Rate</div>
                    <div className="text-right">Amount</div>
                  </div>
                  {invoice.items.map((item) => (
                    <div key={item.id} className="grid grid-cols-5 gap-4 text-sm">
                      <div className="col-span-2">{item.description}</div>
                      <div>{item.hours}</div>
                      <div>{formatCurrency(item.rate)}</div>
                      <div className="text-right font-medium">{formatCurrency(item.amount)}</div>
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal:</span>
                  <span>{formatCurrency(invoice.amount)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Tax (10%):</span>
                  <span>{formatCurrency(invoice.tax_amount)}</span>
                </div>
                <div className="flex justify-between text-lg font-bold">
                  <span>Total:</span>
                  <span>{formatCurrency(invoice.total_amount)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Payment Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h5 className="font-medium mb-2">Payment Terms</h5>
                <p className="text-sm text-muted-foreground">Net 30</p>
              </div>
              <div>
                <h5 className="font-medium mb-2">Payment Methods</h5>
                <div className="text-sm text-muted-foreground space-y-1">
                  <p>Bank Transfer</p>
                  <p>Credit Card</p>
                  <p>Check</p>
                </div>
              </div>
              <div>
                <h5 className="font-medium mb-2">Notes</h5>
                <p className="text-sm text-muted-foreground">
                  Thank you for your business! Please pay within 30 days of the invoice date.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};