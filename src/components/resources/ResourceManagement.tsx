import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, Users, AlertCircle, TrendingUp } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface TeamMember {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  position: string;
  capacity: number;
  current_workload: number;
  skills: string[];
  availability: 'available' | 'busy' | 'unavailable';
}

interface ResourceConflict {
  id: string;
  member_name: string;
  project_name: string;
  conflict_type: 'overallocation' | 'skill_mismatch' | 'unavailable';
  severity: 'low' | 'medium' | 'high';
  description: string;
}

export const ResourceManagement = () => {
  const [selectedTeam, setSelectedTeam] = useState<string>('all');
  const [timeframe, setTimeframe] = useState<string>('week');

  const { data: teamMembers = [], isLoading: loadingMembers } = useQuery({
    queryKey: ['team-members', selectedTeam],
    queryFn: async () => {
      const query = (supabase as any)
        .from('profiles')
        .select(`
          *,
          user_roles!inner(role),
          team_members!inner(
            teams(name, id)
          )
        `);
      
      if (selectedTeam !== 'all') {
        query.eq('team_members.team_id', selectedTeam);
      }

      const { data, error } = await query;
      if (error) throw error;

      return data?.map(member => ({
        id: member.id,
        first_name: member.first_name || '',
        last_name: member.last_name || '',
        email: member.email || '',
        position: member.position || 'Team Member',
        capacity: 40, // hours per week
        current_workload: Math.floor(Math.random() * 45), // Mock data
        skills: ['React', 'TypeScript', 'Node.js'], // Mock data
        availability: ['available', 'busy', 'unavailable'][Math.floor(Math.random() * 3)] as TeamMember['availability']
      })) || [];
    }
  });

  const { data: teams = [] } = useQuery({
    queryKey: ['teams-list'],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('teams')
        .select('id, name')
        .eq('is_active', true);
      
      if (error) throw error;
      return data || [];
    }
  });

  const { data: conflicts = [] } = useQuery({
    queryKey: ['resource-conflicts'],
    queryFn: async () => {
      // Mock data for resource conflicts
      return [
        {
          id: '1',
          member_name: 'John Doe',
          project_name: 'Project Alpha',
          conflict_type: 'overallocation' as const,
          severity: 'high' as const,
          description: 'Allocated 50 hours but capacity is 40 hours'
        },
        {
          id: '2',
          member_name: 'Jane Smith',
          project_name: 'Project Beta',
          conflict_type: 'skill_mismatch' as const,
          severity: 'medium' as const,
          description: 'Required skill: Python, Available skills: React, TypeScript'
        }
      ] as ResourceConflict[];
    }
  });

  const getWorkloadColor = (workload: number, capacity: number) => {
    const percentage = (workload / capacity) * 100;
    if (percentage > 100) return 'destructive';
    if (percentage > 80) return 'secondary';
    return 'default';
  };

  const getAvailabilityColor = (availability: TeamMember['availability']) => {
    switch (availability) {
      case 'available': return 'default';
      case 'busy': return 'secondary';
      case 'unavailable': return 'destructive';
      default: return 'outline';
    }
  };

  const getSeverityColor = (severity: ResourceConflict['severity']) => {
    switch (severity) {
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'default';
      default: return 'outline';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">Resource Management</h2>
        <div className="flex gap-4">
          <Select value={selectedTeam} onValueChange={setSelectedTeam}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Select team" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Teams</SelectItem>
              {teams.map(team => (
                <SelectItem key={team.id} value={team.id}>{team.name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="quarter">This Quarter</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs defaultValue="capacity" className="space-y-4">
        <TabsList>
          <TabsTrigger value="capacity">Capacity Planning</TabsTrigger>
          <TabsTrigger value="conflicts">Resource Conflicts</TabsTrigger>
          <TabsTrigger value="skills">Skills Matrix</TabsTrigger>
        </TabsList>

        <TabsContent value="capacity" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Team Capacity</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{teamMembers.reduce((acc, m) => acc + m.capacity, 0)}h</div>
                <p className="text-xs text-muted-foreground">Total weekly capacity</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Current Load</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{teamMembers.reduce((acc, m) => acc + m.current_workload, 0)}h</div>
                <p className="text-xs text-muted-foreground">Hours allocated</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Utilization</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.round((teamMembers.reduce((acc, m) => acc + m.current_workload, 0) / 
                             teamMembers.reduce((acc, m) => acc + m.capacity, 0)) * 100)}%
                </div>
                <p className="text-xs text-muted-foreground">Team utilization</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Conflicts</CardTitle>
                <AlertCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{conflicts.length}</div>
                <p className="text-xs text-muted-foreground">Resource conflicts</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Team Member Capacity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {teamMembers.map((member) => (
                  <div key={member.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div>
                        <p className="font-medium">{member.first_name} {member.last_name}</p>
                        <p className="text-sm text-muted-foreground">{member.position}</p>
                      </div>
                      <Badge variant={getAvailabilityColor(member.availability)}>
                        {member.availability}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm font-medium">
                          {member.current_workload}h / {member.capacity}h
                        </p>
                        <Progress 
                          value={(member.current_workload / member.capacity) * 100} 
                          className="w-32"
                        />
                      </div>
                      <Badge variant={getWorkloadColor(member.current_workload, member.capacity)}>
                        {Math.round((member.current_workload / member.capacity) * 100)}%
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="conflicts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Resource Conflicts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {conflicts.map((conflict) => (
                  <div key={conflict.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <p className="font-medium">{conflict.member_name}</p>
                        <Badge variant="outline">{conflict.project_name}</Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{conflict.description}</p>
                      <Badge variant="outline">{conflict.conflict_type.replace('_', ' ')}</Badge>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={getSeverityColor(conflict.severity)}>
                        {conflict.severity}
                      </Badge>
                      <Button size="sm">Resolve</Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="skills" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Skills Matrix</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {teamMembers.map((member) => (
                  <div key={member.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <p className="font-medium">{member.first_name} {member.last_name}</p>
                      <p className="text-sm text-muted-foreground">{member.position}</p>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {member.skills.map((skill) => (
                        <Badge key={skill} variant="secondary">{skill}</Badge>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};