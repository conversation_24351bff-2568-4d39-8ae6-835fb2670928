import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { TeamMemberManagement } from './TeamMemberManagement';
import { Calendar, Edit, Users } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface TeamDetailViewProps {
  teamId: string;
  onEdit?: () => void;
}

export const TeamDetailView: React.FC<TeamDetailViewProps> = ({
  teamId,
  onEdit
}) => {
  const { data: team, isLoading, refetch } = useQuery({
    queryKey: ['team', teamId],
    queryFn: async () => {
      console.log('TeamDetailView: Fetching team with ID:', teamId);
      
      try {
        // Get basic team data
        const { data: teamData, error: teamError } = await supabase
          .from('teams')
          .select('*')
          .eq('id', teamId)
          .single();
        
        if (teamError) throw teamError;
        if (!teamData) return null;
        
        // Get team member count
        const { count: memberCount } = await supabase
          .from('team_members')
          .select('*', { count: 'exact', head: true })
          .eq('team_id', teamId);
        
        // Get creator profile
        let creatorProfile = null;
        if (teamData.created_by) {
          const { data: profile } = await supabase
            .from('profiles')
            .select('first_name, last_name')
            .eq('user_id', teamData.created_by)
            .single();
          creatorProfile = profile;
        }
        
        console.log('TeamDetailView: Query result:', { teamData, memberCount, creatorProfile });
        
        return {
          ...teamData,
          team_members: [{ count: memberCount || 0 }],
          profiles: creatorProfile
        };
      } catch (error) {
        console.error('TeamDetailView: Team query failed:', error);
        return null;
      }
    },
  });

  console.log('TeamDetailView: Current state:', { teamId, team, isLoading });

  if (isLoading || !team) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Team Header */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <CardTitle className="text-2xl">{team.name}</CardTitle>
              <Badge variant={team.is_active ? 'default' : 'secondary'}>
                {team.is_active ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            {team.description && (
              <p className="text-muted-foreground">{team.description}</p>
            )}
          </div>
          {onEdit && (
            <Button onClick={onEdit} variant="outline" className="gap-2">
              <Edit className="w-4 h-4" />
              Edit Team
            </Button>
          )}
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                {team.team_members?.[0]?.count || 0} members
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                Created: {team.created_at ? new Date(team.created_at).toLocaleDateString() : 'Unknown'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                Created by: {team.profiles?.first_name} {team.profiles?.last_name}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Team Members Management */}
      <TeamMemberManagement teamId={teamId} onRefresh={refetch} />
    </div>
  );
};