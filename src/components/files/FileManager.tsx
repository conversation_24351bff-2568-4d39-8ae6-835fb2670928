import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { 
  File, 
  Folder, 
  Upload, 
  Download, 
  Share2, 
  Trash2, 
  Eye, 
  History,
  FileText,
  Image,
  Video,
  Archive
} from 'lucide-react';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

interface FileItem {
  id: string;
  name: string;
  type: 'file' | 'folder';
  size: number;
  url?: string;
  project_id?: string;
  task_id?: string;
  uploaded_by: string;
  created_at: string;
  file_type?: string;
  version: number;
  shared_with: string[];
}

interface FileVersion {
  id: string;
  file_id: string;
  version: number;
  url: string;
  uploaded_by: string;
  created_at: string;
  size: number;
}

export const FileManager = () => {
  const queryClient = useQueryClient();
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const { data: files = [], isLoading } = useQuery({
    queryKey: ['files', selectedProject],
    queryFn: async () => {
      let query = (supabase as any)
        .from('file_attachments')
        .select(`
          *,
          profiles!file_attachments_uploaded_by_fkey(first_name, last_name)
        `);
      
      if (selectedProject !== 'all') {
        query = query.eq('project_id', selectedProject);
      }

      const { data, error } = await query.order('created_at', { ascending: false });
      if (error) throw error;

      return data?.map(file => ({
        id: file.id,
        name: file.file_name,
        type: 'file' as const,
        size: file.file_size || 0,
        url: file.file_url,
        project_id: file.project_id,
        task_id: file.task_id,
        uploaded_by: file.uploaded_by,
        created_at: file.created_at,
        file_type: file.file_type,
        version: 1, // Mock version
        shared_with: [] // Mock shared data
      })) || [];
    }
  });

  const { data: projects = [] } = useQuery({
    queryKey: ['projects-for-files'],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('projects')
        .select('id, name')
        .order('name');
      
      if (error) throw error;
      return data || [];
    }
  });

  const { data: fileVersions = [] } = useQuery({
    queryKey: ['file-versions', selectedFile?.id],
    queryFn: async () => {
      if (!selectedFile) return [];
      
      // Mock file versions data
      return [
        {
          id: '1',
          file_id: selectedFile.id,
          version: 1,
          url: selectedFile.url || '',
          uploaded_by: selectedFile.uploaded_by,
          created_at: selectedFile.created_at,
          size: selectedFile.size
        }
      ] as FileVersion[];
    },
    enabled: !!selectedFile
  });

  const uploadMutation = useMutation({
    mutationFn: async ({ file, projectId, taskId }: { file: File; projectId?: string; taskId?: string }) => {
      const fileExt = file.name.split('.').pop();
      const fileName = `${projectId || 'general'}/${Date.now()}-${file.name}`;
      const bucket = projectId ? 'project-files' : 'task-attachments';
      
      try {
        // Real upload to Supabase Storage
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from(bucket)
          .upload(fileName, file);

        if (uploadError) throw uploadError;

        // Get public URL
        const { data: urlData } = supabase.storage
          .from(bucket)
          .getPublicUrl(uploadData.path);

        // Save file metadata to database
        const { data: user } = await supabase.auth.getUser();
        if (!user.user) throw new Error('Not authenticated');

        const { data, error } = await (supabase as any)
          .from('file_attachments')
          .insert({
            file_name: file.name,
            file_url: urlData.publicUrl,
            file_size: file.size,
            file_type: file.type,
            project_id: projectId,
            task_id: taskId,
            uploaded_by: user.user.id
          })
          .select()
          .single();

        if (error) throw error;
        return data;
      } catch (error) {
        console.error('Upload error:', error);
        throw error;
      }
    },
    onSuccess: () => {
      toast.success('File uploaded successfully');
      setUploadProgress(0);
      queryClient.invalidateQueries({ queryKey: ['files'] });
    },
    onError: () => {
      toast.error('Failed to upload file');
      setUploadProgress(0);
    }
  });

  const deleteMutation = useMutation({
    mutationFn: async (fileId: string) => {
      const { error } = await (supabase as any)
        .from('file_attachments')
        .delete()
        .eq('id', fileId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      toast.success('File deleted successfully');
      queryClient.invalidateQueries({ queryKey: ['files'] });
    },
    onError: () => {
      toast.error('Failed to delete file');
    }
  });

  const shareMutation = useMutation({
    mutationFn: async ({ fileId, userIds }: { fileId: string; userIds: string[] }) => {
      // Mock sharing functionality
      await new Promise(resolve => setTimeout(resolve, 1000));
      return userIds;
    },
    onSuccess: () => {
      toast.success('File shared successfully');
    },
    onError: () => {
      toast.error('Failed to share file');
    }
  });

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    uploadMutation.mutate({ 
      file, 
      projectId: selectedProject !== 'all' ? selectedProject : undefined 
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileType: string) => {
    if (fileType?.startsWith('image/')) return <Image className="h-8 w-8" />;
    if (fileType?.startsWith('video/')) return <Video className="h-8 w-8" />;
    if (fileType?.includes('zip') || fileType?.includes('rar')) return <Archive className="h-8 w-8" />;
    return <FileText className="h-8 w-8" />;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">File Manager</h2>
        <div className="flex gap-4">
          <Select value={selectedProject} onValueChange={setSelectedProject}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Projects</SelectItem>
              {projects.map(project => (
                <SelectItem key={project.id} value={project.id}>{project.name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Input
            type="file"
            onChange={handleFileUpload}
            disabled={uploadMutation.isPending}
            className="hidden"
            id="file-upload"
          />
          <label htmlFor="file-upload">
            <Button asChild disabled={uploadMutation.isPending}>
              <span>
                <Upload className="h-4 w-4 mr-2" />
                Upload File
              </span>
            </Button>
          </label>
        </div>
      </div>

      {uploadMutation.isPending && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Uploading...</span>
                <span>{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} />
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="files" className="space-y-4">
        <TabsList>
          <TabsTrigger value="files">Files</TabsTrigger>
          <TabsTrigger value="shared">Shared</TabsTrigger>
          <TabsTrigger value="recent">Recent</TabsTrigger>
        </TabsList>

        <TabsContent value="files" className="space-y-4">
          <div className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground">
              {files.length} files
            </p>
            <div className="flex gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                Grid
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                List
              </Button>
            </div>
          </div>

          {viewMode === 'grid' ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {files.map((file) => (
                <Card key={file.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-center">
                        {getFileIcon(file.file_type || '')}
                      </div>
                      <div className="space-y-1">
                        <p className="font-medium text-sm truncate" title={file.name}>
                          {file.name}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formatFileSize(file.size)}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          v{file.version}
                        </p>
                      </div>
                      <div className="flex justify-between">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <Eye className="h-3 w-3" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>{file.name}</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div className="grid gap-2">
                                <p><strong>Size:</strong> {formatFileSize(file.size)}</p>
                                <p><strong>Version:</strong> {file.version}</p>
                                <p><strong>Created:</strong> {new Date(file.created_at).toLocaleDateString()}</p>
                              </div>
                              <div className="flex gap-2">
                                <Button size="sm">
                                  <Download className="h-3 w-3 mr-1" />
                                  Download
                                </Button>
                                <Button variant="outline" size="sm">
                                  <Share2 className="h-3 w-3 mr-1" />
                                  Share
                                </Button>
                                <Button variant="outline" size="sm">
                                  <History className="h-3 w-3 mr-1" />
                                  Versions
                                </Button>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => deleteMutation.mutate(file.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-0">
                <div className="space-y-1">
                  {files.map((file) => (
                    <div key={file.id} className="flex items-center justify-between p-4 border-b last:border-b-0">
                      <div className="flex items-center space-x-3">
                        {getFileIcon(file.file_type || '')}
                        <div>
                          <p className="font-medium">{file.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {formatFileSize(file.size)} • v{file.version}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">
                          {new Date(file.created_at).toLocaleDateString()}
                        </Badge>
                        <Button variant="outline" size="sm">
                          <Download className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Share2 className="h-3 w-3" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => deleteMutation.mutate(file.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="shared">
          <Card>
            <CardHeader>
              <CardTitle>Shared Files</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Files shared with you will appear here.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recent">
          <Card>
            <CardHeader>
              <CardTitle>Recent Files</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {files.slice(0, 5).map((file) => (
                  <div key={file.id} className="flex items-center space-x-3 p-2 rounded hover:bg-muted">
                    {getFileIcon(file.file_type || '')}
                    <div className="flex-1">
                      <p className="font-medium">{file.name}</p>
                      <p className="text-sm text-muted-foreground">
                        Modified {new Date(file.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};