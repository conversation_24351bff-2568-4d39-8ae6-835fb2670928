import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Workflow, 
  Plus, 
  Settings,
  Play,
  Pause,
  Edit,
  Trash2,
  Zap,
  Clock,
  Mail,
  Bell,
  UserPlus,
  FileText,
  CheckCircle
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthContext';
import { toast } from 'sonner';

interface WorkflowRule {
  id: string;
  name: string;
  description: string;
  trigger_type: 'task_created' | 'task_completed' | 'project_status_change' | 'deadline_approaching' | 'user_assigned';
  trigger_conditions: any;
  actions: WorkflowAction[];
  is_active: boolean;
  created_at: string;
  created_by: string;
}

interface WorkflowAction {
  type: 'send_notification' | 'assign_user' | 'update_status' | 'send_email' | 'create_task';
  config: any;
}

export const WorkflowAutomation: React.FC = () => {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedRule, setSelectedRule] = useState<WorkflowRule | null>(null);
  const [newRule, setNewRule] = useState({
    name: '',
    description: '',
    trigger_type: 'task_created' as const,
    trigger_conditions: {},
    actions: [] as WorkflowAction[],
  });

  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Fetch workflow rules
  const { data: workflowRules = [], isLoading } = useQuery({
    queryKey: ['workflow-rules'],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('workflow_rules')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data || [];
    },
    enabled: !!user,
  });

  // Create workflow rule mutation
  const createRuleMutation = useMutation({
    mutationFn: async (ruleData: any) => {
      const { data, error } = await (supabase as any)
        .from('workflow_rules')
        .insert({
          ...ruleData,
          created_by: user?.id,
          is_active: true,
        });
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workflow-rules'] });
      setIsCreateDialogOpen(false);
      setNewRule({
        name: '',
        description: '',
        trigger_type: 'task_created',
        trigger_conditions: {},
        actions: [],
      });
      toast.success('Workflow rule created successfully');
    },
    onError: () => {
      toast.error('Failed to create workflow rule');
    },
  });

  // Toggle rule status mutation
  const toggleRuleMutation = useMutation({
    mutationFn: async ({ id, is_active }: { id: string; is_active: boolean }) => {
      const { error } = await (supabase as any)
        .from('workflow_rules')
        .update({ is_active })
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workflow-rules'] });
      toast.success('Workflow rule updated');
    },
    onError: () => {
      toast.error('Failed to update workflow rule');
    },
  });

  // Delete rule mutation
  const deleteRuleMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await (supabase as any)
        .from('workflow_rules')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workflow-rules'] });
      toast.success('Workflow rule deleted');
    },
    onError: () => {
      toast.error('Failed to delete workflow rule');
    },
  });

  const addAction = (type: WorkflowAction['type']) => {
    const defaultConfigs = {
      send_notification: { message: '', users: [] },
      assign_user: { user_id: '', role: '' },
      update_status: { status: '' },
      send_email: { subject: '', message: '', recipients: [] },
      create_task: { title: '', description: '', assignee_id: '' },
    };

    setNewRule(prev => ({
      ...prev,
      actions: [...prev.actions, { type, config: defaultConfigs[type] }],
    }));
  };

  const removeAction = (index: number) => {
    setNewRule(prev => ({
      ...prev,
      actions: prev.actions.filter((_, i) => i !== index),
    }));
  };

  const updateActionConfig = (index: number, config: any) => {
    setNewRule(prev => ({
      ...prev,
      actions: prev.actions.map((action, i) => 
        i === index ? { ...action, config } : action
      ),
    }));
  };

  const handleCreateRule = () => {
    if (!newRule.name || !newRule.description || newRule.actions.length === 0) {
      toast.error('Please fill in all required fields and add at least one action');
      return;
    }
    createRuleMutation.mutate(newRule);
  };

  const getTriggerIcon = (type: string) => {
    switch (type) {
      case 'task_created': return <Plus className="w-4 h-4" />;
      case 'task_completed': return <CheckCircle className="w-4 h-4" />;
      case 'project_status_change': return <Settings className="w-4 h-4" />;
      case 'deadline_approaching': return <Clock className="w-4 h-4" />;
      case 'user_assigned': return <UserPlus className="w-4 h-4" />;
      default: return <Zap className="w-4 h-4" />;
    }
  };

  const getActionIcon = (type: string) => {
    switch (type) {
      case 'send_notification': return <Bell className="w-4 h-4" />;
      case 'assign_user': return <UserPlus className="w-4 h-4" />;
      case 'update_status': return <Settings className="w-4 h-4" />;
      case 'send_email': return <Mail className="w-4 h-4" />;
      case 'create_task': return <FileText className="w-4 h-4" />;
      default: return <Zap className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Workflow Automation</h2>
          <p className="text-muted-foreground">
            Automate repetitive tasks with custom workflow rules
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="gap-2">
              <Plus className="w-4 h-4" />
              Create Rule
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create Workflow Rule</DialogTitle>
              <DialogDescription>
                Define triggers and actions for automated workflow management
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-6">
              {/* Basic Info */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Rule Name</label>
                  <Input
                    placeholder="e.g., Auto-assign tasks to team lead"
                    value={newRule.name}
                    onChange={(e) => setNewRule(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Description</label>
                  <Textarea
                    placeholder="Describe what this rule does..."
                    value={newRule.description}
                    onChange={(e) => setNewRule(prev => ({ ...prev, description: e.target.value }))}
                    rows={2}
                  />
                </div>
              </div>

              {/* Trigger */}
              <div className="space-y-4">
                <h4 className="font-medium">Trigger</h4>
                <Select
                  value={newRule.trigger_type}
                  onValueChange={(value: any) => setNewRule(prev => ({ ...prev, trigger_type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="task_created">When a task is created</SelectItem>
                    <SelectItem value="task_completed">When a task is completed</SelectItem>
                    <SelectItem value="project_status_change">When project status changes</SelectItem>
                    <SelectItem value="deadline_approaching">When deadline is approaching</SelectItem>
                    <SelectItem value="user_assigned">When user is assigned</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Actions */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Actions</h4>
                  <Select onValueChange={(value: any) => addAction(value)}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Add action" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="send_notification">Send notification</SelectItem>
                      <SelectItem value="assign_user">Assign user</SelectItem>
                      <SelectItem value="update_status">Update status</SelectItem>
                      <SelectItem value="send_email">Send email</SelectItem>
                      <SelectItem value="create_task">Create task</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-3">
                  {newRule.actions.map((action, index) => (
                    <div key={index} className="border rounded-lg p-4 space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getActionIcon(action.type)}
                          <span className="font-medium capitalize">
                            {action.type.replace('_', ' ')}
                          </span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeAction(index)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                      
                      {action.type === 'send_notification' && (
                        <Input
                          placeholder="Notification message"
                          value={action.config.message}
                          onChange={(e) => updateActionConfig(index, { ...action.config, message: e.target.value })}
                        />
                      )}
                      
                      {action.type === 'send_email' && (
                        <div className="space-y-2">
                          <Input
                            placeholder="Email subject"
                            value={action.config.subject}
                            onChange={(e) => updateActionConfig(index, { ...action.config, subject: e.target.value })}
                          />
                          <Textarea
                            placeholder="Email message"
                            value={action.config.message}
                            onChange={(e) => updateActionConfig(index, { ...action.config, message: e.target.value })}
                            rows={2}
                          />
                        </div>
                      )}
                      
                      {action.type === 'update_status' && (
                        <Select
                          value={action.config.status}
                          onValueChange={(value) => updateActionConfig(index, { ...action.config, status: value })}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="todo">To Do</SelectItem>
                            <SelectItem value="in_progress">In Progress</SelectItem>
                            <SelectItem value="completed">Completed</SelectItem>
                            <SelectItem value="blocked">Blocked</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateRule} disabled={createRuleMutation.isPending}>
                Create Rule
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Workflow Rules List */}
      <div className="grid gap-4">
        {workflowRules.map((rule) => (
          <Card key={rule.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-lg">{rule.name}</CardTitle>
                  <CardDescription>{rule.description}</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={rule.is_active}
                    onCheckedChange={(checked) => 
                      toggleRuleMutation.mutate({ id: rule.id, is_active: checked })
                    }
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteRuleMutation.mutate(rule.id)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  {getTriggerIcon(rule.trigger_type)}
                  <span className="text-sm text-muted-foreground">
                    Trigger: {rule.trigger_type.replace('_', ' ')}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">
                    {rule.actions.length} action(s)
                  </span>
                  <div className="flex gap-1">
                    {rule.actions.slice(0, 3).map((action, index) => (
                      <div key={index} className="p-1 border rounded">
                        {getActionIcon(action.type)}
                      </div>
                    ))}
                    {rule.actions.length > 3 && (
                      <span className="text-xs text-muted-foreground">
                        +{rule.actions.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
                <Badge variant={rule.is_active ? 'default' : 'secondary'}>
                  {rule.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </CardContent>
          </Card>
        ))}

        {workflowRules.length === 0 && (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Workflow className="w-12 h-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No workflow rules yet</h3>
              <p className="text-muted-foreground text-center mb-4">
                Create automated workflow rules to streamline your project management
              </p>
              <Button onClick={() => setIsCreateDialogOpen(true)} className="gap-2">
                <Plus className="w-4 h-4" />
                Create Your First Rule
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};