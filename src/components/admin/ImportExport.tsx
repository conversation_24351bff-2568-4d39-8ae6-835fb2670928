import React, { useState, useRef } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { Upload, Download, FileText, Users, Briefcase, Clock } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface ImportResult {
  total: number;
  successful: number;
  errors: string[];
}

export const ImportExport = () => {
  const queryClient = useQueryClient();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [importType, setImportType] = useState<string>('users');
  const [exportType, setExportType] = useState<string>('projects');
  const [importProgress, setImportProgress] = useState<number>(0);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);

  const exportMutation = useMutation({
    mutationFn: async (type: string) => {
      let data: any[] = [];
      let filename = '';

      switch (type) {
        case 'projects':
          const { data: projects } = await (supabase as any)
            .from('projects')
            .select('*');
          data = projects || [];
          filename = `projects_export_${new Date().toISOString().split('T')[0]}.csv`;
          break;
        case 'tasks':
          const { data: tasks } = await (supabase as any)
            .from('tasks')
            .select('*');
          data = tasks || [];
          filename = `tasks_export_${new Date().toISOString().split('T')[0]}.csv`;
          break;
        case 'users':
          const { data: users } = await (supabase as any)
            .from('profiles')
            .select('*');
          data = users || [];
          filename = `users_export_${new Date().toISOString().split('T')[0]}.csv`;
          break;
        case 'time_entries':
          const { data: timeEntries } = await (supabase as any)
            .from('time_entries')
            .select('*');
          data = timeEntries || [];
          filename = `time_entries_export_${new Date().toISOString().split('T')[0]}.csv`;
          break;
      }

      // Convert to CSV
      if (data.length === 0) {
        throw new Error('No data to export');
      }

      const headers = Object.keys(data[0]);
      const csvContent = [
        headers.join(','),
        ...data.map(row => 
          headers.map(header => {
            const value = row[header];
            if (value === null || value === undefined) return '';
            if (typeof value === 'string' && value.includes(',')) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value;
          }).join(',')
        )
      ].join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      return filename;
    },
    onSuccess: (filename) => {
      toast.success(`Data exported successfully: ${filename}`);
    },
    onError: (error: any) => {
      toast.error(`Export failed: ${error.message}`);
    }
  });

  const importMutation = useMutation({
    mutationFn: async ({ file, type }: { file: File; type: string }) => {
      const text = await file.text();
      const lines = text.split('\n').filter(line => line.trim());
      
      if (lines.length < 2) {
        throw new Error('Invalid CSV file format');
      }

      const headers = lines[0].split(',').map(h => h.trim());
      const rows = lines.slice(1).map(line => {
        const values = line.split(',').map(v => v.trim().replace(/^"|"$/g, ''));
        return headers.reduce((obj, header, index) => {
          obj[header] = values[index] || null;
          return obj;
        }, {} as any);
      });

      let successful = 0;
      const errors: string[] = [];
      const total = rows.length;

      for (let i = 0; i < rows.length; i++) {
        try {
          setImportProgress(((i + 1) / total) * 100);
          
          const row = rows[i];
          
          switch (type) {
            case 'users':
              // Validate required fields
              if (!row.email || !row.first_name) {
                errors.push(`Row ${i + 2}: Missing required fields (email, first_name)`);
                continue;
              }
              
              // Check if user already exists
              const { data: existingUser } = await (supabase as any)
                .from('profiles')
                .select('id')
                .eq('email', row.email)
                .single();
              
              if (existingUser) {
                errors.push(`Row ${i + 2}: User with email ${row.email} already exists`);
                continue;
              }
              
              // Insert user profile (in real app, would also create auth user)
              const { error: profileError } = await (supabase as any)
                .from('profiles')
                .insert({
                  email: row.email,
                  first_name: row.first_name,
                  last_name: row.last_name || null,
                  position: row.position || null,
                  department: row.department || null,
                  phone: row.phone || null
                });
              
              if (profileError) throw profileError;
              break;
              
            case 'projects':
              // Validate required fields
              if (!row.name) {
                errors.push(`Row ${i + 2}: Missing required field (name)`);
                continue;
              }
              
              const { error: projectError } = await (supabase as any)
                .from('projects')
                .insert({
                  name: row.name,
                  description: row.description || null,
                  budget: row.budget ? parseFloat(row.budget) : null,
                  start_date: row.start_date || null,
                  end_date: row.end_date || null,
                  status: row.status || 'planning',
                  created_by: (await supabase.auth.getUser()).data.user?.id
                });
              
              if (projectError) throw projectError;
              break;
          }
          
          successful++;
        } catch (error: any) {
          errors.push(`Row ${i + 2}: ${error.message}`);
        }
      }

      return { total, successful, errors };
    },
    onSuccess: (result) => {
      setImportResult(result);
      setImportProgress(0);
      queryClient.invalidateQueries({ queryKey: [importType] });
      
      if (result.errors.length === 0) {
        toast.success(`Import completed successfully! ${result.successful} records imported.`);
      } else {
        toast.warning(`Import completed with errors. ${result.successful}/${result.total} records imported.`);
      }
    },
    onError: (error: any) => {
      setImportProgress(0);
      toast.error(`Import failed: ${error.message}`);
    }
  });

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    if (!file.name.toLowerCase().endsWith('.csv')) {
      toast.error('Please select a CSV file');
      return;
    }
    
    setImportResult(null);
    importMutation.mutate({ file, type: importType });
  };

  const downloadTemplate = (type: string) => {
    let headers: string[] = [];
    let sampleData: string[] = [];
    
    switch (type) {
      case 'users':
        headers = ['email', 'first_name', 'last_name', 'position', 'department', 'phone'];
        sampleData = ['<EMAIL>', 'John', 'Doe', 'Developer', 'Engineering', '+1234567890'];
        break;
      case 'projects':
        headers = ['name', 'description', 'budget', 'start_date', 'end_date', 'status'];
        sampleData = ['Project Alpha', 'Sample project description', '50000', '2024-01-01', '2024-06-30', 'planning'];
        break;
      case 'tasks':
        headers = ['title', 'description', 'priority', 'estimated_hours', 'due_date'];
        sampleData = ['Sample Task', 'Task description', 'medium', '8', '2024-12-31'];
        break;
    }
    
    const csvContent = [headers.join(','), sampleData.join(',')].join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${type}_template.csv`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">Import & Export</h2>
        <Badge variant="outline">Data Management</Badge>
      </div>

      <Tabs defaultValue="import" className="space-y-4">
        <TabsList>
          <TabsTrigger value="import">Import Data</TabsTrigger>
          <TabsTrigger value="export">Export Data</TabsTrigger>
        </TabsList>

        <TabsContent value="import" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Upload className="h-5 w-5" />
                <span>Import Data from CSV</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label htmlFor="import-type">Data Type</Label>
                  <Select value={importType} onValueChange={setImportType}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="users">Users</SelectItem>
                      <SelectItem value="projects">Projects</SelectItem>
                      <SelectItem value="tasks">Tasks</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Template File</Label>
                  <Button 
                    variant="outline" 
                    onClick={() => downloadTemplate(importType)}
                    className="w-full"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download Template
                  </Button>
                </div>
              </div>

              <div>
                <Label htmlFor="csv-file">CSV File</Label>
                <Input
                  id="csv-file"
                  type="file"
                  accept=".csv"
                  ref={fileInputRef}
                  onChange={handleFileImport}
                  disabled={importMutation.isPending}
                />
              </div>

              {importMutation.isPending && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Importing...</span>
                    <span>{Math.round(importProgress)}%</span>
                  </div>
                  <Progress value={importProgress} />
                </div>
              )}

              {importResult && (
                <Card className="border-l-4 border-l-primary">
                  <CardContent className="pt-6">
                    <div className="space-y-2">
                      <h4 className="font-semibold">Import Results</h4>
                      <p className="text-sm">
                        Successfully imported: <strong>{importResult.successful}</strong> / {importResult.total} records
                      </p>
                      {importResult.errors.length > 0 && (
                        <div className="space-y-1">
                          <p className="text-sm font-medium text-destructive">Errors:</p>
                          <div className="text-xs space-y-1 max-h-32 overflow-y-auto">
                            {importResult.errors.map((error, index) => (
                              <p key={index} className="text-destructive">{error}</p>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="export" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center space-x-2 text-lg">
                  <Briefcase className="h-5 w-5" />
                  <span>Projects</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={() => exportMutation.mutate('projects')}
                  disabled={exportMutation.isPending}
                  className="w-full"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center space-x-2 text-lg">
                  <FileText className="h-5 w-5" />
                  <span>Tasks</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={() => exportMutation.mutate('tasks')}
                  disabled={exportMutation.isPending}
                  className="w-full"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center space-x-2 text-lg">
                  <Users className="h-5 w-5" />
                  <span>Users</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={() => exportMutation.mutate('users')}
                  disabled={exportMutation.isPending}
                  className="w-full"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center space-x-2 text-lg">
                  <Clock className="h-5 w-5" />
                  <span>Time Entries</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={() => exportMutation.mutate('time_entries')}
                  disabled={exportMutation.isPending}
                  className="w-full"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Export Options</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label htmlFor="export-format">Export Format</Label>
                  <Select defaultValue="csv">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="csv">CSV</SelectItem>
                      <SelectItem value="excel">Excel</SelectItem>
                      <SelectItem value="json">JSON</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="date-range">Date Range</Label>
                  <Select defaultValue="all">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Time</SelectItem>
                      <SelectItem value="last-month">Last Month</SelectItem>
                      <SelectItem value="last-quarter">Last Quarter</SelectItem>
                      <SelectItem value="last-year">Last Year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};