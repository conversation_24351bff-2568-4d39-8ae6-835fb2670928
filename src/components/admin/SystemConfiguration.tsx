import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { Settings, Mail, Download, Database, Bell } from 'lucide-react';

interface SystemSetting {
  id: string;
  key: string;
  value: string;
  category: string;
  description: string;
}

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  type: string;
}

export const SystemConfiguration = () => {
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('general');
  const [editingTemplate, setEditingTemplate] = useState<EmailTemplate | null>(null);

  // Mock data for system settings
  const { data: settings = [], isLoading: loadingSettings } = useQuery({
    queryKey: ['system-settings'],
    queryFn: async () => {
      // Mock system settings data
      return [
        {
          id: '1',
          key: 'company_name',
          value: 'RatioHub',
          category: 'general',
          description: 'Company name displayed in the application'
        },
        {
          id: '2',
          key: 'default_currency',
          value: 'USD',
          category: 'billing',
          description: 'Default currency for billing and invoices'
        },
        {
          id: '3',
          key: 'email_notifications',
          value: 'true',
          category: 'notifications',
          description: 'Enable email notifications'
        },
        {
          id: '4',
          key: 'auto_backup',
          value: 'true',
          category: 'backup',
          description: 'Enable automatic daily backups'
        }
      ] as SystemSetting[];
    }
  });

  const { data: emailTemplates = [] } = useQuery({
    queryKey: ['email-templates'],
    queryFn: async () => {
      return [
        {
          id: '1',
          name: 'Welcome Email',
          subject: 'Welcome to RatioHub',
          body: 'Welcome to RatioHub! Your account has been created successfully.',
          type: 'user_welcome'
        },
        {
          id: '2',
          name: 'Task Assignment',
          subject: 'New Task Assigned: {{task_title}}',
          body: 'You have been assigned a new task: {{task_title}} in project {{project_name}}.',
          type: 'task_assignment'
        },
        {
          id: '3',
          name: 'Project Completion',
          subject: 'Project Completed: {{project_name}}',
          body: 'The project {{project_name}} has been marked as completed.',
          type: 'project_completion'
        }
      ] as EmailTemplate[];
    }
  });

  const updateSettingMutation = useMutation({
    mutationFn: async ({ id, value }: { id: string; value: string }) => {
      // Mock update - in real app would update database
      return { id, value };
    },
    onSuccess: () => {
      toast.success('Setting updated successfully');
      queryClient.invalidateQueries({ queryKey: ['system-settings'] });
    },
    onError: () => {
      toast.error('Failed to update setting');
    }
  });

  const updateTemplateMutation = useMutation({
    mutationFn: async (template: EmailTemplate) => {
      // Mock update - in real app would update database
      return template;
    },
    onSuccess: () => {
      toast.success('Email template updated successfully');
      queryClient.invalidateQueries({ queryKey: ['email-templates'] });
      setEditingTemplate(null);
    },
    onError: () => {
      toast.error('Failed to update email template');
    }
  });

  const exportDataMutation = useMutation({
    mutationFn: async (dataType: string) => {
      // Mock export - in real app would generate and download file
      await new Promise(resolve => setTimeout(resolve, 2000));
      return `${dataType}_export_${new Date().toISOString().split('T')[0]}.csv`;
    },
    onSuccess: (filename) => {
      toast.success(`Data exported successfully: ${filename}`);
    },
    onError: () => {
      toast.error('Failed to export data');
    }
  });

  const backupMutation = useMutation({
    mutationFn: async () => {
      // Mock backup process
      await new Promise(resolve => setTimeout(resolve, 3000));
      return `backup_${new Date().toISOString()}.sql`;
    },
    onSuccess: (filename) => {
      toast.success(`Backup created successfully: ${filename}`);
    },
    onError: () => {
      toast.error('Failed to create backup');
    }
  });

  const handleSettingUpdate = (id: string, value: string) => {
    updateSettingMutation.mutate({ id, value });
  };

  const handleTemplateUpdate = (template: EmailTemplate) => {
    updateTemplateMutation.mutate(template);
  };

  const renderSettingsByCategory = (category: string) => {
    const categorySettings = settings.filter(s => s.category === category);
    
    return (
      <div className="space-y-4">
        {categorySettings.map((setting) => (
          <Card key={setting.id}>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-sm font-medium">{setting.key.replace(/_/g, ' ').toUpperCase()}</Label>
                  <p className="text-xs text-muted-foreground">{setting.description}</p>
                </div>
                <div className="w-48">
                  {setting.key.includes('enable') || setting.key.includes('auto') ? (
                    <Switch
                      checked={setting.value === 'true'}
                      onCheckedChange={(checked) => 
                        handleSettingUpdate(setting.id, checked.toString())
                      }
                    />
                  ) : setting.key === 'default_currency' ? (
                    <Select 
                      value={setting.value} 
                      onValueChange={(value) => handleSettingUpdate(setting.id, value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="USD">USD</SelectItem>
                        <SelectItem value="EUR">EUR</SelectItem>
                        <SelectItem value="GBP">GBP</SelectItem>
                      </SelectContent>
                    </Select>
                  ) : (
                    <Input
                      value={setting.value}
                      onChange={(e) => handleSettingUpdate(setting.id, e.target.value)}
                      className="text-right"
                    />
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">System Configuration</h2>
        <Badge variant="outline">Admin Only</Badge>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="email">Email Templates</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="backup">Backup & Export</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          {renderSettingsByCategory('general')}
          {renderSettingsByCategory('billing')}
        </TabsContent>

        <TabsContent value="email" className="space-y-4">
          <div className="grid gap-4">
            {emailTemplates.map((template) => (
              <Card key={template.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{template.name}</CardTitle>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => setEditingTemplate(template)}
                    >
                      Edit
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <p className="text-sm"><strong>Subject:</strong> {template.subject}</p>
                    <p className="text-sm text-muted-foreground">{template.body.substring(0, 100)}...</p>
                    <Badge variant="secondary">{template.type}</Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {editingTemplate && (
            <Card>
              <CardHeader>
                <CardTitle>Edit Email Template</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="template-name">Template Name</Label>
                  <Input
                    id="template-name"
                    value={editingTemplate.name}
                    onChange={(e) => setEditingTemplate({
                      ...editingTemplate,
                      name: e.target.value
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="template-subject">Subject</Label>
                  <Input
                    id="template-subject"
                    value={editingTemplate.subject}
                    onChange={(e) => setEditingTemplate({
                      ...editingTemplate,
                      subject: e.target.value
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="template-body">Body</Label>
                  <Textarea
                    id="template-body"
                    value={editingTemplate.body}
                    onChange={(e) => setEditingTemplate({
                      ...editingTemplate,
                      body: e.target.value
                    })}
                    rows={6}
                  />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button 
                    variant="outline" 
                    onClick={() => setEditingTemplate(null)}
                  >
                    Cancel
                  </Button>
                  <Button 
                    onClick={() => handleTemplateUpdate(editingTemplate)}
                    disabled={updateTemplateMutation.isPending}
                  >
                    {updateTemplateMutation.isPending ? 'Saving...' : 'Save'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          {renderSettingsByCategory('notifications')}
        </TabsContent>

        <TabsContent value="backup" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Database className="h-5 w-5" />
                  <span>Database Backup</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {renderSettingsByCategory('backup')}
                <Button 
                  onClick={() => backupMutation.mutate()}
                  disabled={backupMutation.isPending}
                  className="w-full"
                >
                  {backupMutation.isPending ? 'Creating Backup...' : 'Create Manual Backup'}
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Download className="h-5 w-5" />
                  <span>Data Export</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button 
                  variant="outline" 
                  onClick={() => exportDataMutation.mutate('projects')}
                  disabled={exportDataMutation.isPending}
                  className="w-full"
                >
                  Export Projects
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => exportDataMutation.mutate('tasks')}
                  disabled={exportDataMutation.isPending}
                  className="w-full"
                >
                  Export Tasks
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => exportDataMutation.mutate('time_entries')}
                  disabled={exportDataMutation.isPending}
                  className="w-full"
                >
                  Export Time Entries
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => exportDataMutation.mutate('users')}
                  disabled={exportDataMutation.isPending}
                  className="w-full"
                >
                  Export Users
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="integrations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Integration Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">Calendar Integration</p>
                    <p className="text-sm text-muted-foreground">Sync tasks and deadlines with external calendars</p>
                  </div>
                  <Switch />
                </div>
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">Slack Notifications</p>
                    <p className="text-sm text-muted-foreground">Send notifications to Slack channels</p>
                  </div>
                  <Switch />
                </div>
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">Webhook Endpoints</p>
                    <p className="text-sm text-muted-foreground">Configure webhook URLs for external integrations</p>
                  </div>
                  <Button variant="outline" size="sm">Configure</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};