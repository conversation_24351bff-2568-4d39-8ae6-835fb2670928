import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { Loader2, Users, Shield, MoreHorizontal, Ban } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { UserInvitationForm } from './UserInvitationForm';
import { InvitationManager } from './InvitationManager';
import { BulkUserImport } from './BulkUserImport';

interface User {
  id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  is_active: boolean;
  created_at: string;
  last_login_at: string | null;
  roles: string[];
}

export const AdminUserManagement: React.FC = () => {
  const [email, setEmail] = useState('');
  const queryClient = useQueryClient();

  const { data: users = [], isLoading } = useQuery({
    queryKey: ['all-users'],
    queryFn: async () => {
      const { data: profiles, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      // Get roles for each user
      const userIds = profiles?.map(p => p.user_id) || [];
      const { data: userRoles } = await supabase
        .from('user_roles')
        .select('user_id, role')
        .in('user_id', userIds);
      
      return profiles?.map(user => ({
        ...user,
        roles: userRoles?.filter(r => r.user_id === user.user_id).map(r => r.role) || []
      })) as User[] || [];
    },
  });

  const createAdminUserMutation = useMutation({
    mutationFn: async (userEmail: string) => {
      const { data, error } = await supabase.rpc('create_admin_user', {
        user_email: userEmail
      });

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      if (data) {
        toast.success('User has been granted admin privileges');
        setEmail('');
        queryClient.invalidateQueries({ queryKey: ['all-users'] });
      } else {
        toast.error('User not found with that email address');
      }
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create admin user');
    },
  });

  const toggleUserStatusMutation = useMutation({
    mutationFn: async ({ userId, isActive }: { userId: string; isActive: boolean }) => {
      const { error } = await supabase
        .from('profiles')
        .update({ is_active: !isActive })
        .eq('user_id', userId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      toast.success('User status updated successfully');
      queryClient.invalidateQueries({ queryKey: ['all-users'] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update user status');
    },
  });

  const createAdminUser = () => {
    if (!email) {
      toast.error('Please enter an email address');
      return;
    }
    createAdminUserMutation.mutate(email);
  };

  const getRoleBadges = (roles: string[]) => {
    return roles.map(role => (
      <Badge key={role} variant={role === 'admin' ? 'default' : 'secondary'}>
        {role.replace('_', ' ')}
      </Badge>
    ));
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="users" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="users">All Users</TabsTrigger>
          <TabsTrigger value="invite">Invite User</TabsTrigger>
          <TabsTrigger value="invitations">Invitations</TabsTrigger>
          <TabsTrigger value="bulk">Bulk Import</TabsTrigger>
        </TabsList>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                All Users ({users.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                </div>
              ) : users.length === 0 ? (
                <p className="text-muted-foreground text-center py-8">No users found.</p>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Roles</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Joined</TableHead>
                        <TableHead>Last Login</TableHead>
                        <TableHead className="w-[50px]"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell className="font-medium">
                            {`${user.first_name || ''} ${user.last_name || ''}`.trim() || 'N/A'}
                          </TableCell>
                          <TableCell>{user.email}</TableCell>
                          <TableCell>
                            <div className="flex gap-1 flex-wrap">
                              {getRoleBadges(user.roles)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={user.is_active ? 'default' : 'secondary'}>
                              {user.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                          </TableCell>
                          <TableCell>{format(new Date(user.created_at), 'MMM dd, yyyy')}</TableCell>
                          <TableCell>
                            {user.last_login_at 
                              ? format(new Date(user.last_login_at), 'MMM dd, yyyy')
                              : 'Never'
                            }
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={() => toggleUserStatusMutation.mutate({
                                    userId: user.user_id,
                                    isActive: user.is_active
                                  })}
                                >
                                  <Ban className="mr-2 h-4 w-4" />
                                  {user.is_active ? 'Deactivate' : 'Activate'}
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Legacy Admin Creation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Grant Admin Access
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">User Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter user email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
              <Button 
                onClick={createAdminUser} 
                disabled={createAdminUserMutation.isPending}
                className="w-full"
              >
                {createAdminUserMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Make Admin
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="invite">
          <UserInvitationForm />
        </TabsContent>

        <TabsContent value="invitations">
          <InvitationManager />
        </TabsContent>

        <TabsContent value="bulk">
          <BulkUserImport />
        </TabsContent>
      </Tabs>
    </div>
  );
};