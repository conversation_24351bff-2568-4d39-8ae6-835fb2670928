import React, { useState, useRef } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { Upload, Download, FileText, Users, Loader2 } from 'lucide-react';

interface ImportProgress {
  total: number;
  processed: number;
  successful: number;
  failed: number;
  errors: string[];
}

export const BulkUserImport: React.FC = () => {
  const [file, setFile] = useState<File | null>(null);
  const [importProgress, setImportProgress] = useState<ImportProgress | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const queryClient = useQueryClient();

  const { data: teams = [] } = useQuery({
    queryKey: ['teams'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('teams')
        .select('id, name')
        .eq('is_active', true)
        .order('name');
      
      if (error) throw error;
      return data || [];
    },
  });

  const downloadTemplate = () => {
    const csvContent = [
      'email,role,teams,personal_message',
      '<EMAIL>,user,"Team A;Team B",Welcome to the team!',
      '<EMAIL>,project_manager,Team A,',
      '<EMAIL>,admin,,You have admin access'
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'bulk-invitation-template.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const parseCSV = (csvText: string) => {
    const lines = csvText.split('\n').filter(line => line.trim());
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    
    return lines.slice(1).map((line, index) => {
      const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
      const row: any = {};
      
      headers.forEach((header, i) => {
        row[header] = values[i] || '';
      });
      
      // Parse teams field
      if (row.teams) {
        row.teamIds = row.teams.split(';').map((t: string) => {
          const team = teams.find(team => team.name.toLowerCase() === t.trim().toLowerCase());
          return team?.id;
        }).filter(Boolean);
      } else {
        row.teamIds = [];
      }
      
      row.lineNumber = index + 2; // +2 because we skip header and array is 0-indexed
      return row;
    });
  };

  const bulkImportMutation = useMutation({
    mutationFn: async (users: any[]) => {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new Error('No session');

      const progress: ImportProgress = {
        total: users.length,
        processed: 0,
        successful: 0,
        failed: 0,
        errors: []
      };

      setImportProgress(progress);

      for (const user of users) {
        try {
          if (!user.email || !user.role) {
            throw new Error('Email and role are required');
          }

          const response = await supabase.functions.invoke('send-invitation', {
            body: {
              email: user.email,
              role: user.role,
              teamIds: user.teamIds,
              personalMessage: user.personal_message,
            },
            headers: {
              Authorization: `Bearer ${session.access_token}`,
            },
          });

          if (response.error) throw response.error;
          
          progress.successful++;
        } catch (error: any) {
          progress.failed++;
          progress.errors.push(`Line ${user.lineNumber}: ${error.message}`);
        }
        
        progress.processed++;
        setImportProgress({ ...progress });
        
        // Small delay to prevent rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      return progress;
    },
    onSuccess: (progress) => {
      queryClient.invalidateQueries({ queryKey: ['invitations'] });
      
      if (progress.failed === 0) {
        toast.success(`Successfully sent ${progress.successful} invitations!`);
      } else {
        toast.warning(
          `Sent ${progress.successful} invitations. ${progress.failed} failed.`,
          { description: 'Check the progress details below.' }
        );
      }
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to process bulk import');
    },
    onSettled: () => {
      setIsImporting(false);
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile && selectedFile.type === 'text/csv') {
      setFile(selectedFile);
      setImportProgress(null);
    } else {
      toast.error('Please select a valid CSV file');
      e.target.value = '';
    }
  };

  const handleImport = async () => {
    if (!file) return;

    setIsImporting(true);
    
    try {
      const csvText = await file.text();
      const users = parseCSV(csvText);
      
      if (users.length === 0) {
        throw new Error('No valid users found in CSV file');
      }
      
      await bulkImportMutation.mutateAsync(users);
    } catch (error: any) {
      toast.error(error.message || 'Failed to process CSV file');
      setIsImporting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Bulk User Import
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div>
            <Button onClick={downloadTemplate} variant="outline" className="w-full">
              <Download className="mr-2 h-4 w-4" />
              Download CSV Template
            </Button>
            <p className="text-sm text-muted-foreground mt-2">
              Download the template to see the required format for bulk invitations.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="csv-file">Select CSV File</Label>
            <Input
              id="csv-file"
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleFileChange}
              disabled={isImporting}
            />
          </div>

          <Button
            onClick={handleImport}
            disabled={!file || isImporting}
            className="w-full"
          >
            {isImporting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Import Users
              </>
            )}
          </Button>
        </div>

        {importProgress && (
          <div className="space-y-4 p-4 bg-muted rounded-lg">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span>{importProgress.processed} / {importProgress.total}</span>
              </div>
              <Progress 
                value={(importProgress.processed / importProgress.total) * 100} 
                className="w-full"
              />
            </div>

            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="space-y-1">
                <div className="text-2xl font-bold text-green-600">{importProgress.successful}</div>
                <div className="text-sm text-muted-foreground">Successful</div>
              </div>
              <div className="space-y-1">
                <div className="text-2xl font-bold text-red-600">{importProgress.failed}</div>
                <div className="text-sm text-muted-foreground">Failed</div>
              </div>
              <div className="space-y-1">
                <div className="text-2xl font-bold text-muted-foreground">{importProgress.total - importProgress.processed}</div>
                <div className="text-sm text-muted-foreground">Remaining</div>
              </div>
            </div>

            {importProgress.errors.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium text-destructive">Errors:</h4>
                <div className="max-h-40 overflow-y-auto space-y-1">
                  {importProgress.errors.map((error, index) => (
                    <p key={index} className="text-sm text-destructive bg-destructive/10 p-2 rounded">
                      {error}
                    </p>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};