import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface TaskAssignmentManagerProps {
  taskId: string;
  currentAssignee?: string;
  projectId: string;
  taskTitle: string;
  teamId?: string;
  onAssigneeChange?: (newAssignee: string | null) => void;
}

export const TaskAssignmentManager: React.FC<TaskAssignmentManagerProps> = ({
  taskId,
  currentAssignee,
  projectId,
  taskTitle,
  teamId,
  onAssigneeChange
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch team members
  const { data: teamMembers } = useQuery({
    queryKey: ['team-members', teamId],
    queryFn: async () => {
      if (!teamId) return [];

      const { data, error } = await supabase
        .from('team_members')
        .select(`
          user_id,
          role,
          profiles:user_id(first_name, last_name)
        `)
        .eq('team_id', teamId);

      if (error) throw error;
      return data || [];
    },
    enabled: !!teamId,
  });

  const updateAssignmentMutation = useMutation({
    mutationFn: async (newAssignee: string | null) => {
      // Update task assignment
      const { error } = await supabase
        .from('tasks')
        .update({ assigned_to: newAssignee })
        .eq('id', taskId);

      if (error) throw error;

      // Log activity
      const { data: user } = await supabase.auth.getUser();
      if (user.user) {
        const assigneeName = newAssignee 
          ? teamMembers?.find(m => m.user_id === newAssignee)?.profiles
          : null;
        
        await supabase.rpc('log_project_activity', {
          p_project_id: projectId,
          p_user_id: user.user.id,
          p_activity_type: 'task_assigned',
          p_entity_type: 'task',
          p_entity_id: taskId,
          p_description: newAssignee 
            ? `Assigned task "${taskTitle}" to ${assigneeName?.first_name} ${assigneeName?.last_name}`
            : `Unassigned task "${taskTitle}"`,
          p_metadata: { 
            task_title: taskTitle,
            assigned_to: newAssignee,
            assignee_name: assigneeName ? `${assigneeName.first_name} ${assigneeName.last_name}` : null
          }
        });
      }

      return newAssignee;
    },
    onSuccess: (newAssignee) => {
      queryClient.invalidateQueries({ queryKey: ['project-tasks', projectId] });
      queryClient.invalidateQueries({ queryKey: ['project-activity-feed', projectId] });
      
      onAssigneeChange?.(newAssignee);
      
      toast({
        title: "Assignment Updated",
        description: newAssignee ? "Task assigned successfully" : "Task unassigned",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update task assignment",
        variant: "destructive",
      });
    },
  });

  const getInitials = (firstName?: string, lastName?: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase() || 'U';
  };

  const currentAssigneeData = teamMembers?.find(m => m.user_id === currentAssignee);

  return (
    <div className="flex items-center gap-2">
      <Select
        value={currentAssignee || 'unassigned'}
        onValueChange={(value) => 
          updateAssignmentMutation.mutate(value === 'unassigned' ? null : value)
        }
        disabled={updateAssignmentMutation.isPending}
      >
        <SelectTrigger className="w-auto">
          <SelectValue>
            <div className="flex items-center gap-2">
              <Avatar className="h-6 w-6">
                <AvatarFallback className="text-xs">
                  {currentAssigneeData?.profiles 
                    ? getInitials(currentAssigneeData.profiles.first_name, currentAssigneeData.profiles.last_name)
                    : 'U'
                  }
                </AvatarFallback>
              </Avatar>
              <span className="text-sm">
                {currentAssigneeData?.profiles 
                  ? `${currentAssigneeData.profiles.first_name} ${currentAssigneeData.profiles.last_name}`
                  : 'Unassigned'
                }
              </span>
            </div>
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="unassigned">
            <div className="flex items-center gap-2">
              <Avatar className="h-6 w-6">
                <AvatarFallback className="text-xs">U</AvatarFallback>
              </Avatar>
              <span>Unassigned</span>
            </div>
          </SelectItem>
          {teamMembers?.map((member) => (
            <SelectItem key={member.user_id} value={member.user_id}>
              <div className="flex items-center gap-2">
                <Avatar className="h-6 w-6">
                  <AvatarFallback className="text-xs">
                    {getInitials(member.profiles?.first_name, member.profiles?.last_name)}
                  </AvatarFallback>
                </Avatar>
                <span>{member.profiles?.first_name} {member.profiles?.last_name}</span>
                <span className="text-xs text-muted-foreground">({member.role})</span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {updateAssignmentMutation.isPending && (
        <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
      )}
    </div>
  );
};