import React from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface TaskStatusManagerProps {
  taskId: string;
  currentStatus: string;
  projectId: string;
  taskTitle: string;
  onStatusChange?: (newStatus: string) => void;
}

export const TaskStatusManager: React.FC<TaskStatusManagerProps> = ({
  taskId,
  currentStatus,
  projectId,
  taskTitle,
  onStatusChange
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const updateTaskStatusMutation = useMutation({
    mutationFn: async (newStatus: string) => {
      // Update task status
      const { error } = await supabase
        .from('tasks')
        .update({ status: newStatus as any })
        .eq('id', taskId);

      if (error) throw error;

      // Log activity
      const { data: user } = await supabase.auth.getUser();
      if (user.user) {
        await supabase.rpc('log_project_activity', {
          p_project_id: projectId,
          p_user_id: user.user.id,
          p_activity_type: newStatus === 'completed' ? 'task_completed' : 'task_status_updated',
          p_entity_type: 'task',
          p_entity_id: taskId,
          p_description: `${newStatus === 'completed' ? 'Completed' : 'Updated status of'} task: ${taskTitle}`,
          p_metadata: { 
            task_title: taskTitle, 
            old_status: currentStatus,
            new_status: newStatus 
          }
        });
      }

      return newStatus;
    },
    onSuccess: (newStatus) => {
      queryClient.invalidateQueries({ queryKey: ['project-tasks', projectId] });
      queryClient.invalidateQueries({ queryKey: ['project-activity-feed', projectId] });
      queryClient.invalidateQueries({ queryKey: ['project-stats', projectId] });
      
      onStatusChange?.(newStatus);
      
      toast({
        title: "Status Updated",
        description: `Task marked as ${newStatus.replace('_', ' ')}`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update task status",
        variant: "destructive",
      });
    },
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'review':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'todo':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'blocked':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'todo':
        return 'To Do';
      case 'in_progress':
        return 'In Progress';
      case 'review':
        return 'Review';
      case 'completed':
        return 'Completed';
      case 'blocked':
        return 'Blocked';
      default:
        return status;
    }
  };

  return (
    <div className="flex items-center gap-2">
      <Select
        value={currentStatus}
        onValueChange={(value) => updateTaskStatusMutation.mutate(value)}
        disabled={updateTaskStatusMutation.isPending}
      >
        <SelectTrigger className="w-auto">
          <SelectValue>
            <Badge variant="outline" className={getStatusColor(currentStatus)}>
              {getStatusLabel(currentStatus)}
            </Badge>
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="todo">
            <Badge variant="outline" className={getStatusColor('todo')}>
              To Do
            </Badge>
          </SelectItem>
          <SelectItem value="in_progress">
            <Badge variant="outline" className={getStatusColor('in_progress')}>
              In Progress
            </Badge>
          </SelectItem>
          <SelectItem value="review">
            <Badge variant="outline" className={getStatusColor('review')}>
              Review
            </Badge>
          </SelectItem>
          <SelectItem value="completed">
            <Badge variant="outline" className={getStatusColor('completed')}>
              Completed
            </Badge>
          </SelectItem>
          <SelectItem value="blocked">
            <Badge variant="outline" className={getStatusColor('blocked')}>
              Blocked
            </Badge>
          </SelectItem>
        </SelectContent>
      </Select>
      
      {updateTaskStatusMutation.isPending && (
        <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
      )}
    </div>
  );
};