import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { TaskComments } from './TaskComments';
import { SubtaskCreateSheet } from './SubtaskCreateSheet';
import { 
  Calendar, 
  Clock, 
  User, 
  CheckCircle2, 
  AlertCircle, 
  Plus,
  MessageCircle,
  ListTodo,
  Target,
  Timer
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface TaskDetailViewProps {
  taskId: string;
  onClose?: () => void;
}

export const TaskDetailView: React.FC<TaskDetailViewProps> = ({ taskId, onClose }) => {
  const [showComments, setShowComments] = useState(false);
  const [isSubtaskSheetOpen, setIsSubtaskSheetOpen] = useState(false);

  // Fetch task details
  const { data: task, isLoading, refetch } = useQuery({
    queryKey: ['task', taskId],
    queryFn: async () => {
      try {
        const { data, error } = await (supabase as any)
          .from('tasks')
          .select(`
            *,
            projects(name, project_id),
            milestones(name),
            profiles:assigned_to(first_name, last_name, email),
            creator:created_by(first_name, last_name, email)
          `)
          .eq('id', taskId)
          .single();
        
        if (error) throw error;
        return data;
      } catch (error) {
        console.error('Task query failed:', error);
        return null;
      }
    },
  });

  // Fetch task comments
  const { data: comments = [] } = useQuery({
    queryKey: ['task-comments', taskId],
    queryFn: async () => {
      try {
        const { data, error } = await (supabase as any)
          .from('task_comments')
          .select(`
            *,
            profiles(first_name, last_name, email)
          `)
          .eq('task_id', taskId)
          .order('created_at', { ascending: true });
        
        if (error) throw error;
        return data || [];
      } catch (error) {
        console.warn('Comments query failed:', error);
        return [];
      }
    },
  });

  // Fetch subtasks (tasks with parent_task_id)
  const { data: subtasks = [], refetch: refetchSubtasks } = useQuery({
    queryKey: ['subtasks', taskId],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from('tasks')
          .select(`
            *,
            profiles:assigned_to(first_name, last_name, email)
          `)
          .eq('parent_task_id', taskId)
          .order('created_at', { ascending: true });
        
        if (error) throw error;
        return data || [];
      } catch (error) {
        console.warn('Subtasks query failed:', error);
        return [];
      }
    },
  });

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-3/4"></div>
          <div className="h-4 bg-muted rounded w-1/2"></div>
          <div className="h-20 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  if (!task) {
    return (
      <div className="p-6 text-center">
        <h3 className="text-lg font-medium mb-2">Task Not Found</h3>
        <p className="text-muted-foreground">The task you're looking for doesn't exist.</p>
      </div>
    );
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500 text-white';
      case 'in_progress': return 'bg-blue-500 text-white';
      case 'review': return 'bg-purple-500 text-white';
      case 'blocked': return 'bg-red-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const completedSubtasks = subtasks.filter(st => st.status === 'completed').length;
  const subtaskProgress = subtasks.length > 0 ? (completedSubtasks / subtasks.length) * 100 : 0;

  const getInitials = (firstName?: string, lastName?: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase() || 'U';
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="font-mono">
              {task.task_id}
            </Badge>
            <Badge className={getPriorityColor(task.priority)}>
              {task.priority}
            </Badge>
            <Badge className={getStatusColor(task.status)}>
              {task.status.replace('_', ' ')}
            </Badge>
          </div>
          <h1 className="text-2xl font-bold">{task.title}</h1>
          <p className="text-muted-foreground">
            in {task.projects?.name} ({task.projects?.project_id})
            {task.milestones && ` • ${task.milestones.name}`}
          </p>
        </div>
        {onClose && (
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        )}
      </div>

      {/* Task Info Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">Assigned To</span>
            </div>
            {task.profiles ? (
              <div className="flex items-center gap-2 mt-2">
                <Avatar className="w-6 h-6">
                  <AvatarFallback className="text-xs">
                    {getInitials(task.profiles.first_name, task.profiles.last_name)}
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm">
                  {task.profiles.first_name} {task.profiles.last_name}
                </span>
              </div>
            ) : (
              <span className="text-sm text-muted-foreground">Unassigned</span>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">Due Date</span>
            </div>
            <p className="text-sm mt-2">
              {task.due_date ? format(new Date(task.due_date), 'MMM d, yyyy') : 'No due date'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Timer className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">Time Tracking</span>
            </div>
            <p className="text-sm mt-2">
              {task.actual_hours || 0}h / {task.estimated_hours || 0}h
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">Progress</span>
            </div>
            <div className="mt-2">
              <Progress value={subtaskProgress} className="h-2" />
              <p className="text-xs text-muted-foreground mt-1">
                {completedSubtasks}/{subtasks.length} subtasks
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Description */}
      {task.description && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Description</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="whitespace-pre-wrap text-sm">
              {task.description}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Subtasks */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <ListTodo className="w-5 h-5" />
              Subtasks ({subtasks.length})
            </CardTitle>
            <Button
              onClick={() => setIsSubtaskSheetOpen(true)}
              className="gap-2"
              size="sm"
            >
              <Plus className="w-4 h-4" />
              Add Subtask
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {subtasks.length > 0 ? (
            <div className="space-y-3">
              {subtasks.map((subtask: any) => (
                <div key={subtask.id} className="flex items-center gap-3 p-3 border rounded-lg">
                  <div className={cn(
                    "w-4 h-4 rounded border-2 flex items-center justify-center",
                    subtask.status === 'completed' ? "bg-green-500 border-green-500" : "border-muted-foreground"
                  )}>
                    {subtask.status === 'completed' && <CheckCircle2 className="w-3 h-3 text-white" />}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className={cn(
                        "font-medium",
                        subtask.status === 'completed' && "line-through text-muted-foreground"
                      )}>
                        {subtask.title}
                      </span>
                      <Badge variant="outline" className="text-xs font-mono">
                        {subtask.task_id}
                      </Badge>
                      <Badge className={cn("text-xs", getPriorityColor(subtask.priority))}>
                        {subtask.priority}
                      </Badge>
                    </div>
                    {subtask.description && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {subtask.description}
                      </p>
                    )}
                  </div>
                  {subtask.profiles && (
                    <Avatar className="w-6 h-6">
                      <AvatarFallback className="text-xs">
                        {getInitials(subtask.profiles.first_name, subtask.profiles.last_name)}
                      </AvatarFallback>
                    </Avatar>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <ListTodo className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p className="text-sm">No subtasks yet</p>
              <p className="text-xs">Break this task down into smaller pieces</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Comments Section */}
      <div className="flex items-center gap-2 mb-4">
        <Button
          variant={showComments ? "default" : "outline"}
          onClick={() => setShowComments(!showComments)}
          className="gap-2"
        >
          <MessageCircle className="w-4 h-4" />
          {showComments ? 'Hide Comments' : `Show Comments (${comments.length})`}
        </Button>
      </div>

      {showComments && (
        <TaskComments
          taskId={taskId}
        />
      )}

      {/* Subtask Creation Sheet */}
      <SubtaskCreateSheet
        open={isSubtaskSheetOpen}
        onOpenChange={setIsSubtaskSheetOpen}
        parentTaskId={taskId}
        projectId={task?.project_id || ''}
      />
    </div>
  );
};