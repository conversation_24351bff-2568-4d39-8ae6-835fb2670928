import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { 
  Clock, 
  Edit, 
  Trash2, 
  Plus, 
  Filter, 
  Calendar as CalendarIcon,
  DollarSign,
  Download,
  Search
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { format, parseISO, startOfWeek, endOfWeek, isWithinInterval } from 'date-fns';
import { cn } from '@/lib/utils';

interface TimeEntry {
  id: string;
  start_time: string;
  end_time: string | null;
  duration_minutes: number | null;
  description: string | null;
  is_billable: boolean;
  hourly_rate: number | null;
  project_id: string;
  task_id: string | null;
  projects: {
    name: string;
    project_id: string;
  };
  tasks?: {
    title: string;
    task_id: string;
  } | null;
}

export const TimeEntries: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [projectFilter, setProjectFilter] = useState('');
  const [billableFilter, setBillableFilter] = useState('');
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: startOfWeek(new Date()),
    to: endOfWeek(new Date()),
  });
  const [selectedEntry, setSelectedEntry] = useState<TimeEntry | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch time entries
  const { data: timeEntries = [], isLoading } = useQuery({
    queryKey: ['time-entries', user?.id, dateRange],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('time_entries')
        .select(`
          *,
          projects(name, project_id),
          tasks(title, task_id)
        `)
        .eq('user_id', user!.id)
        .gte('start_time', dateRange.from.toISOString())
        .lte('start_time', dateRange.to.toISOString())
        .order('start_time', { ascending: false });
      
      if (error) throw error;
      return data as TimeEntry[];
    },
    enabled: !!user,
  });

  // Fetch projects for filter
  const { data: projects = [] } = useQuery({
    queryKey: ['user-projects'],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('projects')
        .select('id, name, project_id')
        .order('name');
      
      if (error) throw error;
      return data;
    },
    enabled: !!user,
  });

  const deleteEntryMutation = useMutation({
    mutationFn: async (entryId: string) => {
      const { error } = await (supabase as any)
        .from('time_entries')
        .delete()
        .eq('id', entryId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['time-entries'] });
      toast({
        title: 'Entry deleted',
        description: 'Time entry has been deleted successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error deleting entry',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const formatDuration = (minutes: number | null) => {
    if (!minutes) return '0h 0m';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const calculateTotal = (entries: TimeEntry[]) => {
    const totalMinutes = entries.reduce((sum, entry) => sum + (entry.duration_minutes || 0), 0);
    const billableMinutes = entries
      .filter(entry => entry.is_billable)
      .reduce((sum, entry) => sum + (entry.duration_minutes || 0), 0);
    const totalEarnings = entries
      .filter(entry => entry.is_billable && entry.hourly_rate)
      .reduce((sum, entry) => {
        const hours = (entry.duration_minutes || 0) / 60;
        return sum + (hours * (entry.hourly_rate || 0));
      }, 0);

    return {
      totalHours: formatDuration(totalMinutes),
      billableHours: formatDuration(billableMinutes),
      totalEarnings,
    };
  };

  // Filter entries
  const filteredEntries = timeEntries.filter(entry => {
    const matchesSearch = !searchQuery || 
      entry.projects.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      entry.tasks?.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      entry.description?.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesProject = !projectFilter || entry.project_id === projectFilter;
    
    const matchesBillable = !billableFilter || 
      (billableFilter === 'billable' && entry.is_billable) ||
      (billableFilter === 'non-billable' && !entry.is_billable);

    return matchesSearch && matchesProject && matchesBillable;
  });

  const totals = calculateTotal(filteredEntries);

  const exportToCSV = () => {
    const headers = ['Date', 'Project', 'Task', 'Description', 'Duration', 'Billable', 'Rate', 'Earnings'];
    const csvData = filteredEntries.map(entry => [
      format(parseISO(entry.start_time), 'yyyy-MM-dd'),
      entry.projects.name,
      entry.tasks?.title || '',
      entry.description || '',
      formatDuration(entry.duration_minutes),
      entry.is_billable ? 'Yes' : 'No',
      entry.hourly_rate ? `$${entry.hourly_rate}` : '',
      entry.is_billable && entry.hourly_rate ? 
        `$${((entry.duration_minutes || 0) / 60 * entry.hourly_rate).toFixed(2)}` : '',
    ]);

    const csv = [headers, ...csvData].map(row => row.join(',')).join('\n');
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `time-entries-${format(new Date(), 'yyyy-MM-dd')}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              Time Entries
            </CardTitle>
            <CardDescription>
              View and manage your time tracking records
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={exportToCSV} className="gap-2">
              <Download className="w-4 h-4" />
              Export CSV
            </Button>
            <Button className="gap-2">
              <Plus className="w-4 h-4" />
              Manual Entry
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 border rounded-lg bg-muted/20">
            <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground mb-1">
              <Clock className="w-4 h-4" />
              Total Hours
            </div>
            <div className="text-2xl font-bold">{totals.totalHours}</div>
          </div>
          <div className="p-4 border rounded-lg bg-muted/20">
            <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground mb-1">
              <Badge className="w-4 h-4" />
              Billable Hours
            </div>
            <div className="text-2xl font-bold">{totals.billableHours}</div>
          </div>
          <div className="p-4 border rounded-lg bg-muted/20">
            <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground mb-1">
              <DollarSign className="w-4 h-4" />
              Total Earnings
            </div>
            <div className="text-2xl font-bold">${totals.totalEarnings.toFixed(2)}</div>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-4 p-4 border rounded-lg bg-muted/20">
          <div className="flex-1 min-w-[200px]">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search entries..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <Select value={projectFilter} onValueChange={setProjectFilter}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Filter by project" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Projects</SelectItem>
              {projects.map((project) => (
                <SelectItem key={project.id} value={project.id}>
                  {project.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={billableFilter} onValueChange={setBillableFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Billable status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Entries</SelectItem>
              <SelectItem value="billable">Billable</SelectItem>
              <SelectItem value="non-billable">Non-billable</SelectItem>
            </SelectContent>
          </Select>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="gap-2">
                <CalendarIcon className="w-4 h-4" />
                {format(dateRange.from, 'MMM d')} - {format(dateRange.to, 'MMM d')}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <Calendar
                mode="range"
                selected={dateRange}
                onSelect={(range) => {
                  if (range?.from && range?.to) {
                    setDateRange({ from: range.from, to: range.to });
                  }
                }}
                numberOfMonths={1}
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* Entries List */}
        <div className="space-y-3">
          {isLoading ? (
            <div className="text-center py-8 text-muted-foreground">Loading entries...</div>
          ) : filteredEntries.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Clock className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p>No time entries found</p>
              <p className="text-sm">Start tracking time to see entries here</p>
            </div>
          ) : (
            filteredEntries.map((entry) => (
              <div 
                key={entry.id} 
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/20 transition-colors"
              >
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-medium">{entry.projects.name}</h4>
                    <Badge variant="outline" className="text-xs">
                      {entry.projects.project_id}
                    </Badge>
                    {entry.tasks && (
                      <Badge variant="secondary" className="text-xs">
                        {entry.tasks.title}
                      </Badge>
                    )}
                    {entry.is_billable ? (
                      <Badge className="bg-success text-xs">Billable</Badge>
                    ) : (
                      <Badge variant="outline" className="text-xs">Non-billable</Badge>
                    )}
                  </div>
                  {entry.description && (
                    <p className="text-sm text-muted-foreground mb-1">{entry.description}</p>
                  )}
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span>{format(parseISO(entry.start_time), 'MMM d, h:mm a')}</span>
                    {entry.end_time && (
                      <span>- {format(parseISO(entry.end_time), 'h:mm a')}</span>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium text-lg">
                    {formatDuration(entry.duration_minutes)}
                  </div>
                  {entry.is_billable && entry.hourly_rate && (
                    <div className="text-sm text-muted-foreground">
                      ${entry.hourly_rate}/hr • $
                      {((entry.duration_minutes || 0) / 60 * entry.hourly_rate).toFixed(2)}
                    </div>
                  )}
                  <div className="flex gap-1 mt-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedEntry(entry);
                        setIsEditModalOpen(true);
                      }}
                    >
                      <Edit className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deleteEntryMutation.mutate(entry.id)}
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};