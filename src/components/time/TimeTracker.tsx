import React, { useState, useEffect, useCallback } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Play, Pause, Square, Clock, DollarSign } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';

interface ActiveTimer {
  id: string;
  project_id: string;
  task_id?: string;
  description?: string;
  start_time: string;
  is_billable: boolean;
  hourly_rate?: number;
}

interface Project {
  id: string;
  name: string;
  project_id: string;
  hourly_rate?: number;
  is_billable: boolean;
}

interface Task {
  id: string;
  title: string;
  task_id: string;
  project_id: string;
}

export const TimeTracker: React.FC = () => {
  const [isTracking, setIsTracking] = useState(false);
  const [currentTime, setCurrentTime] = useState(0); // seconds
  const [selectedProject, setSelectedProject] = useState('');
  const [selectedTask, setSelectedTask] = useState('');
  const [description, setDescription] = useState('');
  const [isBillable, setIsBillable] = useState(true);
  const [hourlyRate, setHourlyRate] = useState<number>(0);
  const [activeTimer, setActiveTimer] = useState<ActiveTimer | null>(null);

  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch projects
  const { data: projects = [] } = useQuery({
    queryKey: ['user-projects'],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('projects')
        .select('id, name, project_id, hourly_rate, is_billable')
        .order('name');
      
      if (error) throw error;
      return data as Project[];
    },
    enabled: !!user,
  });

  // Fetch tasks for selected project
  const { data: tasks = [] } = useQuery({
    queryKey: ['project-tasks', selectedProject],
    queryFn: async () => {
      if (!selectedProject) return [];
      
      const { data, error } = await (supabase as any)
        .from('tasks')
        .select('id, title, task_id, project_id')
        .eq('project_id', selectedProject)
        .order('title');
      
      if (error) throw error;
      return data as Task[];
    },
    enabled: !!selectedProject,
  });

  // Check for active timer on mount
  useEffect(() => {
    const checkActiveTimer = async () => {
      if (!user) return;

      const { data, error } = await (supabase as any)
        .from('time_entries')
        .select('*')
        .eq('user_id', user.id)
        .is('end_time', null)
        .order('start_time', { ascending: false })
        .limit(1)
        .single();

      if (data && !error) {
        setActiveTimer(data);
        setIsTracking(true);
        setSelectedProject(data.project_id);
        setSelectedTask(data.task_id || '');
        setDescription(data.description || '');
        setIsBillable(data.is_billable);
        setHourlyRate(data.hourly_rate || 0);
      }
    };

    checkActiveTimer();
  }, [user]);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isTracking && activeTimer) {
      interval = setInterval(() => {
        const startTime = new Date(activeTimer.start_time);
        const elapsed = Math.floor((Date.now() - startTime.getTime()) / 1000);
        setCurrentTime(elapsed);
      }, 1000);
    }

    return () => clearInterval(interval);
  }, [isTracking, activeTimer]);

  // Update project-specific settings
  useEffect(() => {
    const project = projects.find(p => p.id === selectedProject);
    if (project) {
      setIsBillable(project.is_billable);
      setHourlyRate(project.hourly_rate || 0);
    }
  }, [selectedProject, projects]);

  const formatTime = useCallback((seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  const startTimerMutation = useMutation({
    mutationFn: async () => {
      if (!selectedProject) throw new Error('Please select a project');
      
      const { data, error } = await (supabase as any)
        .from('time_entries')
        .insert({
          user_id: user!.id,
          project_id: selectedProject,
          task_id: selectedTask || null,
          description: description.trim() || null,
          start_time: new Date().toISOString(),
          is_billable: isBillable,
          hourly_rate: isBillable ? hourlyRate : null,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      setActiveTimer(data);
      setIsTracking(true);
      setCurrentTime(0);
      toast({
        title: 'Timer started',
        description: 'Time tracking has begun for this task.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error starting timer',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const stopTimerMutation = useMutation({
    mutationFn: async () => {
      if (!activeTimer) throw new Error('No active timer');

      const endTime = new Date();
      const startTime = new Date(activeTimer.start_time);
      const durationMinutes = Math.floor((endTime.getTime() - startTime.getTime()) / (1000 * 60));

      const { data, error } = await (supabase as any)
        .from('time_entries')
        .update({
          end_time: endTime.toISOString(),
          duration_minutes: durationMinutes,
        })
        .eq('id', activeTimer.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      setActiveTimer(null);
      setIsTracking(false);
      setCurrentTime(0);
      setSelectedProject('');
      setSelectedTask('');
      setDescription('');
      queryClient.invalidateQueries({ queryKey: ['time-entries'] });
      toast({
        title: 'Timer stopped',
        description: 'Time entry has been saved successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error stopping timer',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const handleStartStop = () => {
    if (isTracking) {
      stopTimerMutation.mutate();
    } else {
      startTimerMutation.mutate();
    }
  };

  const calculateEarnings = useCallback(() => {
    if (!isBillable || !hourlyRate) return 0;
    const hours = currentTime / 3600;
    return hours * hourlyRate;
  }, [currentTime, isBillable, hourlyRate]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="w-5 h-5" />
          Time Tracker
        </CardTitle>
        <CardDescription>
          Track time for your current task with automatic billing calculation
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Timer Display */}
        <div className="text-center py-8 bg-muted/30 rounded-lg">
          <div className="text-5xl font-mono font-bold text-primary mb-2">
            {formatTime(currentTime)}
          </div>
          {isBillable && hourlyRate > 0 && (
            <div className="flex items-center justify-center gap-2 text-lg text-muted-foreground">
              <DollarSign className="w-4 h-4" />
              ${calculateEarnings().toFixed(2)}
            </div>
          )}
          <div className="flex justify-center gap-4 mt-6">
            <Button 
              size="lg" 
              onClick={handleStartStop}
              disabled={startTimerMutation.isPending || stopTimerMutation.isPending}
              className={isTracking ? "bg-destructive hover:bg-destructive/90" : ""}
            >
              {isTracking ? (
                <>
                  <Square className="w-5 h-5 mr-2" />
                  Stop Timer
                </>
              ) : (
                <>
                  <Play className="w-5 h-5 mr-2" />
                  Start Timer
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Timer Configuration */}
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="project">Project *</Label>
              <Select 
                value={selectedProject} 
                onValueChange={setSelectedProject}
                disabled={isTracking}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select project" />
                </SelectTrigger>
                <SelectContent>
                  {projects.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.name} ({project.project_id})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="task">Task (Optional)</Label>
              <Select 
                value={selectedTask} 
                onValueChange={setSelectedTask}
                disabled={isTracking || !selectedProject}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select task" />
                </SelectTrigger>
                <SelectContent>
                  {tasks.map((task) => (
                    <SelectItem key={task.id} value={task.id}>
                      {task.title} ({task.task_id})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea 
              id="description"
              placeholder="What are you working on?"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              disabled={isTracking}
              className="resize-none"
              rows={2}
            />
          </div>

          <div className="flex items-center justify-between p-4 border rounded-lg bg-muted/20">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Switch 
                  id="billable" 
                  checked={isBillable}
                  onCheckedChange={setIsBillable}
                  disabled={isTracking}
                />
                <Label htmlFor="billable">Billable Time</Label>
                {isBillable && <Badge variant="default">Billable</Badge>}
              </div>
              {isBillable && (
                <div className="flex items-center gap-2">
                  <Label htmlFor="rate" className="text-sm">Rate:</Label>
                  <div className="flex items-center">
                    <span className="text-sm mr-1">$</span>
                    <Input
                      id="rate"
                      type="number"
                      value={hourlyRate}
                      onChange={(e) => setHourlyRate(Number(e.target.value))}
                      disabled={isTracking}
                      className="w-20"
                      min="0"
                      step="0.01"
                    />
                    <span className="text-sm ml-1">/hr</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {activeTimer && (
          <div className="p-4 border rounded-lg bg-primary/5">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock className="w-4 h-4" />
              Started at {format(new Date(activeTimer.start_time), 'h:mm a')}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};