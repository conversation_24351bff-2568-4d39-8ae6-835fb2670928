import { supabase } from '@/integrations/supabase/client';

export interface SecurityEventData {
  event_type: string;
  user_id?: string;
  ip_address?: string;
  user_agent?: string;
  metadata?: Record<string, any>;
}

export const logSecurityEvent = async (eventData: SecurityEventData) => {
  try {
    const { error } = await (supabase as any).rpc('log_security_event', {
      p_event_type: eventData.event_type,
      p_user_id: eventData.user_id || null,
      p_ip_address: eventData.ip_address || null,
      p_user_agent: eventData.user_agent || navigator.userAgent,
      p_metadata: eventData.metadata || null,
    });

    if (error) {
      console.error('Failed to log security event:', error);
    }
  } catch (error) {
    console.error('Error logging security event:', error);
  }
};

// Utility functions for common security events
export const SecurityEvents = {
  loginAttempt: (userId?: string, success: boolean = true) => 
    logSecurityEvent({
      event_type: success ? 'login_success' : 'login_failed',
      user_id: userId,
      metadata: { success }
    }),

  logout: (userId?: string) => 
    logSecurityEvent({
      event_type: 'logout',
      user_id: userId
    }),

  passwordChange: (userId?: string) => 
    logSecurityEvent({
      event_type: 'password_changed',
      user_id: userId
    }),

  roleAssignment: (role: string, userId?: string, assignedBy?: string) => 
    logSecurityEvent({
      event_type: 'role_assigned',
      user_id: userId,
      metadata: { role, assigned_by: assignedBy }
    }),

  suspiciousActivity: (activityType: string, userId?: string, details?: any) => 
    logSecurityEvent({
      event_type: 'suspicious_activity',
      user_id: userId,
      metadata: { activity_type: activityType, details }
    })
};