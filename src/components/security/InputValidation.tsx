import { z } from 'zod';

// Enhanced validation schemas with security in mind
export const secureEmailSchema = z
  .string()
  .min(5, 'Email must be at least 5 characters')
  .max(254, 'Email must not exceed 254 characters')
  .email('Please enter a valid email address')
  .refine((email) => !email.includes('<') && !email.includes('>'), {
    message: 'Email contains invalid characters'
  });

export const securePasswordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters')
  .max(128, 'Password must not exceed 128 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number')
  .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character');

export const secureTextSchema = (maxLength: number = 1000) =>
  z
    .string()
    .max(maxLength, `Text must not exceed ${maxLength} characters`)
    .refine((text) => !text.includes('<script'), {
      message: 'Text contains potentially dangerous content'
    })
    .refine((text) => !text.includes('javascript:'), {
      message: 'Text contains potentially dangerous content'
    })
    .transform((text) => text.trim());

export const secureNameSchema = z
  .string()
  .min(1, 'Name is required')
  .max(100, 'Name must not exceed 100 characters')
  .regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes')
  .transform((name) => name.trim());

export const projectNameSchema = z
  .string()
  .min(2, 'Project name must be at least 2 characters')
  .max(100, 'Project name must not exceed 100 characters')
  .regex(/^[a-zA-Z0-9\s\-_]+$/, 'Project name can only contain letters, numbers, spaces, hyphens, and underscores')
  .transform((name) => name.trim());

// Utility function to sanitize user input
export const sanitizeInput = (input: string, maxLength: number = 1000): string => {
  if (!input) return '';
  
  // Remove potentially dangerous characters
  const sanitized = input
    .replace(/[<>\"'&]/g, '')
    .trim()
    .slice(0, maxLength);
  
  return sanitized;
};

// Rate limiting utility (client-side basic implementation)
export class RateLimiter {
  private attempts: Map<string, number[]> = new Map();
  private maxAttempts: number;
  private windowMs: number;

  constructor(maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000) {
    this.maxAttempts = maxAttempts;
    this.windowMs = windowMs;
  }

  isAllowed(key: string): boolean {
    const now = Date.now();
    const attempts = this.attempts.get(key) || [];
    
    // Remove old attempts outside the window
    const validAttempts = attempts.filter(time => now - time < this.windowMs);
    
    if (validAttempts.length >= this.maxAttempts) {
      return false;
    }
    
    // Add current attempt
    validAttempts.push(now);
    this.attempts.set(key, validAttempts);
    
    return true;
  }

  getRemainingTime(key: string): number {
    const attempts = this.attempts.get(key) || [];
    if (attempts.length === 0) return 0;
    
    const oldestAttempt = Math.min(...attempts);
    const remainingTime = this.windowMs - (Date.now() - oldestAttempt);
    
    return Math.max(0, Math.ceil(remainingTime / 1000));
  }
}

export const authRateLimiter = new RateLimiter(5, 15 * 60 * 1000); // 5 attempts per 15 minutes