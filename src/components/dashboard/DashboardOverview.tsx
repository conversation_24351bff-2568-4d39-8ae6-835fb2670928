import React from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  BarChart3, 
  <PERSON>olderK<PERSON>ban, 
  Users, 
  Timer, 
  TicketIcon, 
  TrendingUp,
  Plus,
  Activity
} from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

export const DashboardOverview = () => {
  // Fetch real dashboard stats
  const { data: stats } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: async () => {
      try {
        // Get projects count
        const { count: projectsCount } = await (supabase as any)
          .from('projects')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'active');

        // Get team members count
        const { count: membersCount } = await (supabase as any)
          .from('profiles')
          .select('*', { count: 'exact', head: true })
          .eq('is_active', true);

        // Get time entries for this week
        const startOfWeek = new Date();
        startOfWeek.setDate(startOfWeek.getDate() - 7);
        
        const { data: timeEntries } = await (supabase as any)
          .from('time_entries')
          .select('duration_minutes')
          .gte('created_at', startOfWeek.toISOString());

        const totalHours = timeEntries?.reduce((sum: number, entry: any) => 
          sum + (entry.duration_minutes || 0), 0) / 60 || 0;

        // Get open tickets count
        const { count: ticketsCount } = await (supabase as any)
          .from('support_tickets')
          .select('*', { count: 'exact', head: true })
          .in('status', ['open', 'in_progress']);

        return {
          activeProjects: projectsCount || 0,
          teamMembers: membersCount || 0,
          hoursTracked: totalHours.toFixed(1),
          openTickets: ticketsCount || 0
        };
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        return {
          activeProjects: 0,
          teamMembers: 0,
          hoursTracked: '0.0',
          openTickets: 0
        };
      }
    },
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  const statsCards = [
    {
      title: "Active Projects",
      value: stats?.activeProjects.toString() || "0",
      change: "Real-time data",
      icon: FolderKanban,
      color: "text-blue-600"
    },
    {
      title: "Team Members",
      value: stats?.teamMembers.toString() || "0",
      change: "Active users",
      icon: Users,
      color: "text-green-600"
    },
    {
      title: "Hours Tracked",
      value: stats?.hoursTracked || "0.0",
      change: "This week",
      icon: Timer,
      color: "text-orange-600"
    },
    {
      title: "Open Tickets",
      value: stats?.openTickets.toString() || "0",
      change: "Needs attention",
      icon: TicketIcon,
      color: "text-red-600"
    }
  ];

  const recentProjects = [
    {
      id: "WE001",
      name: "Website Redesign",
      status: "active",
      progress: 75,
      team: "Design Team",
      dueDate: "Dec 15, 2024"
    },
    {
      id: "MO002",
      name: "Mobile App Development",
      status: "planning",
      progress: 25,
      team: "Dev Team",
      dueDate: "Jan 30, 2025"
    },
    {
      id: "DA003",
      name: "Data Migration",
      status: "active",
      progress: 50,
      team: "Backend Team",
      dueDate: "Dec 20, 2024"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'planning':
        return 'bg-blue-100 text-blue-800';
      case 'on_hold':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome to your project management overview
          </p>
        </div>
        <Button className="gap-2">
          <Plus className="w-4 h-4" />
          New Project
        </Button>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className={`w-4 h-4 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.change}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Projects */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FolderKanban className="w-5 h-5" />
              Recent Projects
            </CardTitle>
            <CardDescription>
              Your latest project activities and progress
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentProjects.map((project) => (
                <div key={project.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium">{project.name}</span>
                      <Badge variant="secondary" className="text-xs">
                        {project.id}
                      </Badge>
                      <Badge className={`text-xs ${getStatusColor(project.status)}`}>
                        {project.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {project.team} • Due {project.dueDate}
                    </p>
                    <div className="w-full bg-secondary rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full" 
                        style={{ width: `${project.progress}%` }}
                      />
                    </div>
                  </div>
                  <div className="ml-4 text-right">
                    <span className="text-sm font-medium">{project.progress}%</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Activity Feed */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Latest updates across your projects
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <div className="flex-1">
                  <p className="text-sm">
                    <span className="font-medium">Sarah Johnson</span> completed task 
                    <span className="font-medium"> "User Interface Design"</span>
                  </p>
                  <p className="text-xs text-muted-foreground">2 hours ago</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div className="flex-1">
                  <p className="text-sm">
                    <span className="font-medium">New project</span> 
                    <span className="font-medium"> "DA003 - Data Migration"</span> was created
                  </p>
                  <p className="text-xs text-muted-foreground">4 hours ago</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                <div className="flex-1">
                  <p className="text-sm">
                    <span className="font-medium">Mike Chen</span> logged 
                    <span className="font-medium">6.5 hours</span> for backend development
                  </p>
                  <p className="text-xs text-muted-foreground">1 day ago</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                <div className="flex-1">
                  <p className="text-sm">
                    <span className="font-medium">Support ticket</span> 
                    <span className="font-medium">"TKT-003"</span> was resolved
                  </p>
                  <p className="text-xs text-muted-foreground">2 days ago</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Get started with common tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-20 flex-col gap-2">
              <FolderKanban className="w-5 h-5" />
              Create Project
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2">
              <Users className="w-5 h-5" />
              Add Team Member
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2">
              <TicketIcon className="w-5 h-5" />
              Create Ticket
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};