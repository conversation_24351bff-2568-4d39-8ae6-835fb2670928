import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Plus, 
  Calendar, 
  CheckCircle2, 
  AlertCircle, 
  Target,
  Flag
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { MilestoneCreateSheet } from './MilestoneCreateSheet';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface ProjectMilestonesProps {
  projectId: string;
  phaseId?: string;
  showPhaseFilter?: boolean;
}

export const ProjectMilestones: React.FC<ProjectMilestonesProps> = ({ 
  projectId, 
  phaseId, 
  showPhaseFilter = true 
}) => {
  const [isMilestoneSheetOpen, setIsMilestoneSheetOpen] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch milestones for the project or specific phase
  const { data: milestones, isLoading, refetch } = useQuery({
    queryKey: ['project-milestones', projectId, phaseId],
    queryFn: async () => {
      let query = supabase
        .from('milestones')
        .select('*')
        .order('due_date', { ascending: true });

      if (phaseId) {
        query = query.eq('phase_id', phaseId);
      } else {
        query = query.eq('project_id', projectId);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data || [];
    },
    enabled: !!projectId
  });

  // Fetch tasks per milestone for progress calculation
  const { data: milestoneTasks } = useQuery({
    queryKey: ['milestone-tasks', projectId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tasks')
        .select('milestone_id, status')
        .eq('project_id', projectId)
        .not('milestone_id', 'is', null);

      if (error) throw error;
      
      // Group tasks by milestone
      const tasksByMilestone = {};
      data?.forEach(task => {
        if (!tasksByMilestone[task.milestone_id]) {
          tasksByMilestone[task.milestone_id] = [];
        }
        tasksByMilestone[task.milestone_id].push(task);
      });

      return tasksByMilestone;
    },
    enabled: !!projectId
  });

  // Update milestone completion mutation
  const updateMilestoneMutation = useMutation({
    mutationFn: async ({ milestoneId, isCompleted }: { milestoneId: string; isCompleted: boolean }) => {
      const updates: any = { 
        is_completed: isCompleted,
        updated_at: new Date().toISOString()
      };
      
      if (isCompleted) {
        updates.completed_at = new Date().toISOString();
      } else {
        updates.completed_at = null;
        updates.completed_by = null;
      }

      const { error } = await supabase
        .from('milestones')
        .update(updates)
        .eq('id', milestoneId);

      if (error) throw error;
    },
    onSuccess: () => {
      toast({ title: 'Milestone updated successfully' });
      refetch();
    },
    onError: (error) => {
      toast({ 
        title: 'Error updating milestone', 
        description: error.message,
        variant: 'destructive' 
      });
    }
  });

  const handleMilestoneToggle = (milestoneId: string, isCompleted: boolean) => {
    updateMilestoneMutation.mutate({ milestoneId, isCompleted });
  };

  const getMilestoneProgress = (milestoneId: string) => {
    const tasks = milestoneTasks?.[milestoneId] || [];
    if (tasks.length === 0) return 0;
    
    const completedTasks = tasks.filter(task => task.status === 'completed').length;
    return (completedTasks / tasks.length) * 100;
  };

  const getMilestoneTaskCount = (milestoneId: string) => {
    const tasks = milestoneTasks?.[milestoneId] || [];
    const completed = tasks.filter(task => task.status === 'completed').length;
    return { total: tasks.length, completed };
  };

  const getStatusColor = (milestone: any) => {
    if (milestone.is_completed) {
      return 'bg-green-500';
    }
    
    if (milestone.due_date) {
      const dueDate = new Date(milestone.due_date);
      const today = new Date();
      const daysUntilDue = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
      
      if (daysUntilDue < 0) return 'bg-red-500'; // Overdue
      if (daysUntilDue <= 3) return 'bg-orange-500'; // Due soon
      if (daysUntilDue <= 7) return 'bg-yellow-500'; // Due this week
    }
    
    return 'bg-blue-500'; // In progress
  };

  const getStatusText = (milestone: any) => {
    if (milestone.is_completed) return 'Completed';
    
    if (milestone.due_date) {
      const dueDate = new Date(milestone.due_date);
      const today = new Date();
      const daysUntilDue = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
      
      if (daysUntilDue < 0) return 'Overdue';
      if (daysUntilDue <= 3) return 'Due Soon';
      if (daysUntilDue <= 7) return 'Due This Week';
    }
    
    return 'In Progress';
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Project Milestones</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <p>Loading milestones...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Flag className="h-5 w-5" />
            Project Milestones ({milestones?.length || 0})
          </CardTitle>
          <Button 
            size="sm"
            onClick={() => setIsMilestoneSheetOpen(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Milestone
          </Button>
        </CardHeader>
        <CardContent>
          {!milestones || milestones.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Target className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>No milestones found for this project</p>
              <p className="text-sm mt-2">Create your first milestone to track progress</p>
            </div>
          ) : (
            <div className="space-y-4">
              {milestones.map((milestone) => {
                const progress = getMilestoneProgress(milestone.id);
                const taskCount = getMilestoneTaskCount(milestone.id);
                
                return (
                  <div 
                    key={milestone.id} 
                    className="border rounded-lg p-4 space-y-3"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1">
                        <Checkbox
                          checked={milestone.is_completed}
                          onCheckedChange={(checked) => 
                            handleMilestoneToggle(milestone.id, !!checked)
                          }
                          className="mt-1"
                        />
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className={cn(
                              "font-semibold",
                              milestone.is_completed && "line-through text-muted-foreground"
                            )}>
                              {milestone.name}
                            </h3>
                            <Badge className={getStatusColor(milestone)} variant="secondary">
                              {getStatusText(milestone)}
                            </Badge>
                          </div>
                          
                          {milestone.description && (
                            <p className="text-sm text-muted-foreground mb-3">
                              {milestone.description}
                            </p>
                          )}

                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            {milestone.due_date && (
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                <span>Due {format(new Date(milestone.due_date), 'MMM dd, yyyy')}</span>
                              </div>
                            )}
                            
                            <div className="flex items-center gap-1">
                              <CheckCircle2 className="h-3 w-3" />
                              <span>{taskCount.completed}/{taskCount.total} tasks</span>
                            </div>
                            
                            {milestone.completed_at && (
                              <div className="flex items-center gap-1">
                                <span>Completed {format(new Date(milestone.completed_at), 'MMM dd')}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    {taskCount.total > 0 && (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span>Progress</span>
                          <span>{Math.round(progress)}%</span>
                        </div>
                        <Progress value={progress} className="h-2" />
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      <MilestoneCreateSheet
        open={isMilestoneSheetOpen}
        onOpenChange={setIsMilestoneSheetOpen}
        onMilestoneCreated={() => {
          refetch();
          setIsMilestoneSheetOpen(false);
        }}
        projectId={projectId}
        phaseId={phaseId}
      />
    </>
  );
};