import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { PhaseCreateSheet } from './PhaseCreateSheet';
import { ProjectMilestones } from './ProjectMilestones';
import { MoreHorizontal, Plus, Calendar, Target, Clock } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { format } from 'date-fns';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface ProjectPhasesProps {
  projectId: string;
}

interface Phase {
  id: string;
  name: string;
  description: string;
  status: string;
  start_date: string | null;
  end_date: string | null;
  order_index: number;
  created_at: string;
  milestones?: any[];
}

export const ProjectPhases: React.FC<ProjectPhasesProps> = ({ projectId }) => {
  const [createPhaseOpen, setCreatePhaseOpen] = useState(false);
  const [expandedPhases, setExpandedPhases] = useState<Set<string>>(new Set());

  const { data: phases = [], isLoading } = useQuery({
    queryKey: ['project-phases', projectId],
    queryFn: async () => {
      const { data: phases, error } = await supabase
        .from('phases')
        .select('*')
        .eq('project_id', projectId)
        .order('order_index');

      if (error) throw error;

      // Get milestones for each phase separately to avoid join issues
      const phasesWithMilestones = await Promise.all(
        (phases || []).map(async (phase) => {
          const { data: milestones } = await supabase
            .from('milestones')
            .select('id, name, is_completed, due_date')
            .eq('phase_id', phase.id);

          return {
            ...phase,
            milestones: milestones || []
          };
        })
      );

      return phasesWithMilestones as Phase[];
    },
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planning':
        return 'bg-blue-100 text-blue-800';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'on_hold':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPhaseProgress = (phase: Phase) => {
    if (!phase.milestones || phase.milestones.length === 0) return 0;
    const completedCount = phase.milestones.filter(m => m.is_completed).length;
    return (completedCount / phase.milestones.length) * 100;
  };

  const togglePhaseExpansion = (phaseId: string) => {
    const newExpanded = new Set(expandedPhases);
    if (newExpanded.has(phaseId)) {
      newExpanded.delete(phaseId);
    } else {
      newExpanded.add(phaseId);
    }
    setExpandedPhases(newExpanded);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading phases...</CardTitle>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Project Phases</h3>
          <p className="text-sm text-muted-foreground">
            Organize your project into manageable phases with milestones
          </p>
        </div>
        <Button onClick={() => setCreatePhaseOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Phase
        </Button>
      </div>

      {phases.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Target className="h-12 w-12 text-muted-foreground mb-4" />
            <h4 className="text-lg font-medium mb-2">No phases yet</h4>
            <p className="text-muted-foreground mb-4 text-center">
              Create your first phase to start organizing your project milestones
            </p>
            <Button onClick={() => setCreatePhaseOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create First Phase
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {phases.map((phase) => (
            <Card key={phase.id} className="overflow-hidden">
              <Collapsible 
                open={expandedPhases.has(phase.id)}
                onOpenChange={() => togglePhaseExpansion(phase.id)}
              >
                <CollapsibleTrigger asChild>
                  <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <CardTitle className="text-xl">{phase.name}</CardTitle>
                          <Badge className={getStatusColor(phase.status)}>
                            {phase.status.replace('_', ' ')}
                          </Badge>
                        </div>
                        
                        {phase.description && (
                          <p className="text-muted-foreground text-sm mb-3">
                            {phase.description}
                          </p>
                        )}

                        <div className="flex items-center gap-6 text-sm text-muted-foreground">
                          {phase.start_date && (
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              <span>Start: {format(new Date(phase.start_date), 'MMM dd, yyyy')}</span>
                            </div>
                          )}
                          {phase.end_date && (
                            <div className="flex items-center gap-1">
                              <Clock className="h-4 w-4" />
                              <span>End: {format(new Date(phase.end_date), 'MMM dd, yyyy')}</span>
                            </div>
                          )}
                          <div className="flex items-center gap-1">
                            <Target className="h-4 w-4" />
                            <span>{phase.milestones?.length || 0} milestones</span>
                          </div>
                        </div>

                        {phase.milestones && phase.milestones.length > 0 && (
                          <div className="mt-3">
                            <div className="flex items-center justify-between text-sm mb-1">
                              <span>Progress</span>
                              <span>{Math.round(getPhaseProgress(phase))}%</span>
                            </div>
                            <Progress value={getPhaseProgress(phase)} className="h-2" />
                          </div>
                        )}
                      </div>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>Edit Phase</DropdownMenuItem>
                          <DropdownMenuItem className="text-destructive">
                            Delete Phase
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <CardContent className="pt-0">
                    <div className="border-t pt-4">
                      <div className="mb-4">
                        <h4 className="text-md font-medium mb-2">Phase Milestones</h4>
                        <p className="text-sm text-muted-foreground mb-4">
                          Track progress through key milestones in this phase
                        </p>
                      </div>
                      <ProjectMilestones 
                        projectId={projectId} 
                        phaseId={phase.id}
                        showPhaseFilter={false}
                      />
                    </div>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          ))}
        </div>
      )}

      <PhaseCreateSheet
        open={createPhaseOpen}
        onOpenChange={setCreatePhaseOpen}
        projectId={projectId}
        onPhaseCreated={() => {
          // Phase will be automatically reloaded due to query invalidation
        }}
      />
    </div>
  );
};