import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Clock, CheckCircle2, FileText, MessageCircle, User, Target } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { formatDistanceToNow } from 'date-fns';

interface ProjectActivityFeedProps {
  projectId: string;
  limit?: number;
}

export const ProjectActivityFeed: React.FC<ProjectActivityFeedProps> = ({ 
  projectId, 
  limit = 10 
}) => {
  const { data: activities, isLoading } = useQuery({
    queryKey: ['project-activity-feed', projectId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('project_activity_logs')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;

      // Get user profiles separately
      const activitiesWithProfiles = await Promise.all(
        (data || []).map(async (activity) => {
          let userProfile = null;
          if (activity.user_id) {
            const { data: profile } = await supabase
              .from('profiles')
              .select('first_name, last_name')
              .eq('user_id', activity.user_id)
              .single();
            userProfile = profile;
          }
          return { ...activity, profiles: userProfile };
        })
      );

      return activitiesWithProfiles;
    },
    enabled: !!projectId,
  });

  const getActivityIcon = (activityType: string, entityType: string) => {
    switch (entityType) {
      case 'task':
        return activityType === 'task_completed' ? CheckCircle2 : Target;
      case 'milestone':
        return CheckCircle2;
      case 'file':
        return FileText;
      case 'comment':
        return MessageCircle;
      default:
        return Clock;
    }
  };

  const getActivityColor = (activityType: string) => {
    switch (activityType) {
      case 'task_completed':
      case 'milestone_completed':
        return 'text-green-600';
      case 'task_created':
      case 'milestone_created':
        return 'text-blue-600';
      case 'file_uploaded':
        return 'text-purple-600';
      case 'comment_added':
        return 'text-orange-600';
      default:
        return 'text-muted-foreground';
    }
  };

  const getActivityBadgeVariant = (activityType: string) => {
    switch (activityType) {
      case 'task_completed':
      case 'milestone_completed':
        return 'default';
      case 'task_created':
      case 'milestone_created':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-start gap-3 animate-pulse">
                <div className="w-8 h-8 bg-muted rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent>
        {!activities || activities.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Clock className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>No recent activity</p>
          </div>
        ) : (
          <div className="space-y-4">
            {activities.map((activity) => {
              const IconComponent = getActivityIcon(activity.activity_type, activity.entity_type);
              
              return (
                <div key={activity.id} className="flex items-start gap-3 pb-3 border-b border-border last:border-0">
                  <div className={`mt-1 ${getActivityColor(activity.activity_type)}`}>
                    <IconComponent className="h-4 w-4" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">
                          {activity.profiles?.first_name?.[0]}{activity.profiles?.last_name?.[0]}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm font-medium">
                        {activity.profiles?.first_name} {activity.profiles?.last_name}
                      </span>
                      <Badge variant={getActivityBadgeVariant(activity.activity_type)} className="text-xs">
                        {activity.activity_type.replace('_', ' ')}
                      </Badge>
                    </div>
                    
                    <p className="text-sm text-foreground mb-1">
                      {activity.description}
                    </p>
                    
                    <p className="text-xs text-muted-foreground">
                      {formatDistanceToNow(new Date(activity.created_at), { addSuffix: true })}
                    </p>
                    
                    {activity.metadata && (
                      <div className="mt-2 text-xs text-muted-foreground">
                        {Object.entries(activity.metadata as Record<string, any>).map(([key, value]) => (
                          <span key={key} className="mr-3">
                            {key}: {String(value)}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
};