import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import {
    Sheet,
    SheetContent,
    SheetDescription,
    SheetHeader,
    SheetTitle,
} from '@/components/ui/sheet';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, Loader2 } from 'lucide-react';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const taskSchema = z.object({
  title: z.string().min(1, 'Task title is required'),
  description: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  due_date: z.date().optional(),
  estimated_hours: z.number().positive().optional(),
  milestone_id: z.string().optional(),
  assigned_to: z.string().optional(),
  tags: z.string().optional(),
});

type TaskFormData = z.infer<typeof taskSchema>;

interface TaskCreateSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTaskCreated: () => void;
  projectId: string;
}

export const TaskCreateSheet: React.FC<TaskCreateSheetProps> = ({
  open,
  onOpenChange,
  onTaskCreated,
  projectId,
}) => {
  const { toast } = useToast();

  const form = useForm<TaskFormData>({
    resolver: zodResolver(taskSchema),
    defaultValues: {
      title: '',
      description: '',
      priority: 'medium',
    },
  });

  // Fetch milestones for the project
  const { data: milestones } = useQuery({
    queryKey: ['milestones', projectId],
    queryFn: async () => {
      try {
        const { data, error } = await (supabase as any)
          .from('milestones')
          .select('id, name')
          .eq('project_id', projectId)
          .order('created_at');
        
        if (error) throw error;
        return data || [];
      } catch (error) {
        console.warn('Milestones query failed:', error);
        return [];
      }
    },
    enabled: !!projectId,
  });

  // Fetch team members for assignment
  const { data: teamMembers } = useQuery({
    queryKey: ['team-members', projectId],
    queryFn: async () => {
      try {
        const { data, error } = await (supabase as any)
          .from('projects')
          .select(`
            team_id,
            teams!inner(
              team_members!inner(
                user_id,
                profiles!inner(
                  first_name,
                  last_name,
                  email
                )
              )
            )
          `)
          .eq('id', projectId)
          .single();
        
        if (error) throw error;
        return data?.teams?.team_members || [];
      } catch (error) {
        console.warn('Team members query failed:', error);
        return [];
      }
    },
    enabled: !!projectId,
  });

  const createTaskMutation = useMutation({
    mutationFn: async (data: TaskFormData) => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('Not authenticated');

      const taskData = {
        title: data.title,
        description: data.description || null,
        priority: data.priority || 'medium',
        due_date: data.due_date?.toISOString() || null,
        estimated_hours: data.estimated_hours || null,
        milestone_id: data.milestone_id || null,
        assigned_to: data.assigned_to || null,
        project_id: projectId,
        created_by: user.user.id,
        status: 'todo',
        tags: data.tags ? data.tags.split(',').map(tag => tag.trim()) : null,
      };

      const { data: task, error } = await (supabase as any)
        .from('tasks')
        .insert(taskData)
        .select()
        .single();

      if (error) throw error;
      return task;
    },
    onSuccess: (task: any) => {
      toast({
        title: 'Task created successfully',
        description: `${task?.title} (${task?.task_id}) has been created.`,
      });
      form.reset();
      onTaskCreated();
    },
    onError: (error: any) => {
      toast({
        title: 'Error creating task',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const onSubmit = (data: TaskFormData) => {
    createTaskMutation.mutate(data);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="w-[675px] max-w-[90vw] p-0">
        <div className="flex h-full flex-col">
          <div className="flex-shrink-0 p-6 pb-4">
            <SheetHeader className="mb-0">
              <SheetTitle>Create New Task</SheetTitle>
              <SheetDescription>
                Add a new task to this project with details and assignments.
              </SheetDescription>
            </SheetHeader>
          </div>

          <div className="flex-1 overflow-y-auto scrollbar-hidden px-6 pb-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Task Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter task title..." {...field} />
                      </FormControl>
                      <FormDescription>
                        A unique task ID will be generated automatically.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Describe the task requirements..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Priority & Assignment */}
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="priority"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Priority</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select priority" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="urgent">Urgent</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="assigned_to"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Assign To</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select team member" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {teamMembers?.map((member: any) => (
                              <SelectItem key={member.user_id} value={member.user_id}>
                                {member.profiles.first_name} {member.profiles.last_name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Timeline & Milestone */}
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="due_date"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Due Date</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick due date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date < new Date(new Date().setHours(0, 0, 0, 0))
                              }
                              initialFocus
                              className={cn("p-3 pointer-events-auto")}
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="milestone_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Milestone</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select milestone" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {milestones?.map((milestone: any) => (
                              <SelectItem key={milestone.id} value={milestone.id}>
                                {milestone.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Additional Details */}
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="estimated_hours"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Estimated Hours</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="0"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="tags"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tags</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="frontend, backend, design"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Separate tags with commas
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

            </form>
          </Form>
          </div>

          {/* Actions - Fixed Footer */}
          <div className="flex-shrink-0 flex justify-end space-x-2 pt-4 px-6 border-t bg-background">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={createTaskMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              onClick={form.handleSubmit(onSubmit)}
              disabled={createTaskMutation.isPending}
              className="gap-2"
            >
              {createTaskMutation.isPending && (
                <Loader2 className="h-4 w-4 animate-spin" />
              )}
              Create Task
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};