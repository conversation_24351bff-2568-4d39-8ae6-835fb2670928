import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { CalendarDays, Clock, Users, CheckCircle2, AlertCircle, DollarSign } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { format } from 'date-fns';
import { ProjectActivityFeed } from './ProjectActivityFeed';

interface ProjectOverviewProps {
  project: any;
}

export const ProjectOverview: React.FC<ProjectOverviewProps> = ({ project }) => {
  // Fetch project statistics
  const { data: stats } = useQuery({
    queryKey: ['project-stats', project?.id],
    queryFn: async () => {
      if (!project?.id) return null;

      const [tasksResult, milestonesResult, teamResult, timeResult] = await Promise.all([
        supabase
          .from('tasks')
          .select('status, estimated_hours, actual_hours')
          .eq('project_id', project.id),
        supabase
          .from('milestones')
          .select('is_completed')
          .eq('project_id', project.id),
        supabase
          .from('team_members')
          .select('id')
          .eq('team_id', project.team_id),
        supabase
          .from('time_entries')
          .select('duration_minutes')
          .eq('project_id', project.id)
      ]);

      const tasks = tasksResult.data || [];
      const milestones = milestonesResult.data || [];
      const teamMembers = teamResult.data || [];
      const timeEntries = timeResult.data || [];

      const completedTasks = tasks.filter(t => t.status === 'completed').length;
      const totalTasks = tasks.length;
      const taskProgress = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

      const completedMilestones = milestones.filter(m => m.is_completed).length;
      const totalMilestones = milestones.length;

      const totalEstimatedHours = tasks.reduce((sum, task) => sum + (task.estimated_hours || 0), 0);
      const totalActualHours = tasks.reduce((sum, task) => sum + (task.actual_hours || 0), 0);
      const totalTimeLogged = timeEntries.reduce((sum, entry) => sum + (entry.duration_minutes || 0), 0) / 60;

      return {
        tasks: {
          total: totalTasks,
          completed: completedTasks,
          progress: taskProgress,
          inProgress: tasks.filter(t => t.status === 'in_progress').length,
          todo: tasks.filter(t => t.status === 'todo').length
        },
        milestones: {
          total: totalMilestones,
          completed: completedMilestones
        },
        team: {
          memberCount: teamMembers.length
        },
        time: {
          estimated: totalEstimatedHours,
          actual: totalActualHours,
          logged: totalTimeLogged
        }
      };
    },
    enabled: !!project?.id
  });

  if (!project) {
    return (
      <div className="p-8 text-center text-muted-foreground">
        <p>No project data available</p>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'in_progress': return 'bg-blue-500';
      case 'on_hold': return 'bg-yellow-500';
      case 'cancelled': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'planning': return 'Planning';
      case 'in_progress': return 'In Progress';
      case 'on_hold': return 'On Hold';
      case 'completed': return 'Completed';
      case 'cancelled': return 'Cancelled';
      default: return 'Unknown';
    }
  };

  return (
    <div className="space-y-6">
      {/* Project Header */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div className="space-y-2">
              <CardTitle className="text-2xl">{project.name}</CardTitle>
              {project.description && (
                <p className="text-muted-foreground">{project.description}</p>
              )}
            </div>
            <Badge className={getStatusColor(project.status)} variant="secondary">
              {getStatusText(project.status)}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {project.start_date && (
              <div className="flex items-center gap-2">
                <CalendarDays className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Start Date</p>
                  <p className="text-sm text-muted-foreground">
                    {format(new Date(project.start_date), 'MMM dd, yyyy')}
                  </p>
                </div>
              </div>
            )}
            {project.end_date && (
              <div className="flex items-center gap-2">
                <CalendarDays className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">End Date</p>
                  <p className="text-sm text-muted-foreground">
                    {format(new Date(project.end_date), 'MMM dd, yyyy')}
                  </p>
                </div>
              </div>
            )}
            {project.budget && (
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Budget</p>
                  <p className="text-sm text-muted-foreground">
                    ${project.budget.toLocaleString()} {project.currency}
                  </p>
                </div>
              </div>
            )}
            {stats?.team.memberCount !== undefined && (
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Team Members</p>
                  <p className="text-sm text-muted-foreground">
                    {stats.team.memberCount} members
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Statistics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Task Progress */}
        {stats?.tasks && (
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Task Progress</CardTitle>
              <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="text-2xl font-bold">
                  {stats.tasks.completed}/{stats.tasks.total}
                </div>
                <Progress value={stats.tasks.progress} className="h-2" />
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>Todo: {stats.tasks.todo}</span>
                  <span>In Progress: {stats.tasks.inProgress}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Milestones */}
        {stats?.milestones && (
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Milestones</CardTitle>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="text-2xl font-bold">
                  {stats.milestones.completed}/{stats.milestones.total}
                </div>
                <div className="text-sm text-muted-foreground">
                  {stats.milestones.total === 0 ? 'No milestones yet' : 'milestones completed'}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Time Tracking */}
        {stats?.time && (
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Time Tracking</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="text-2xl font-bold">
                  {Math.round(stats.time.logged)}h
                </div>
                <div className="text-sm text-muted-foreground">
                  {stats.time.estimated > 0 && (
                    <div>Estimated: {Math.round(stats.time.estimated)}h</div>
                  )}
                  {stats.time.actual > 0 && (
                    <div>Actual: {Math.round(stats.time.actual)}h</div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Recent Activity Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ProjectActivityFeed projectId={project.id} limit={8} />
        
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <button className="w-full text-left p-3 rounded-lg border hover:bg-muted transition-colors">
              <div className="font-medium text-sm">View All Tasks</div>
              <div className="text-xs text-muted-foreground">See project tasks across all phases</div>
            </button>
            <button className="w-full text-left p-3 rounded-lg border hover:bg-muted transition-colors">
              <div className="font-medium text-sm">Add New Phase</div>
              <div className="text-xs text-muted-foreground">Create a new project phase</div>
            </button>
            <button className="w-full text-left p-3 rounded-lg border hover:bg-muted transition-colors">
              <div className="font-medium text-sm">Upload Files</div>
              <div className="text-xs text-muted-foreground">Add project documents and files</div>
            </button>
            <button className="w-full text-left p-3 rounded-lg border hover:bg-muted transition-colors">
              <div className="font-medium text-sm">Create Support Ticket</div>
              <div className="text-xs text-muted-foreground">Report issues or request features</div>
            </button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};