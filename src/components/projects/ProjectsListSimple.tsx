import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Folder, Plus } from 'lucide-react';

interface ProjectsListSimpleProps {
  projects: any[];
  isLoading: boolean;
  onRefresh: () => void;
  onCreateProject: () => void;
}

export const ProjectsListSimple: React.FC<ProjectsListSimpleProps> = ({ 
  projects, 
  isLoading,
  onRefresh,
  onCreateProject
}) => {
  const navigate = useNavigate();
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="space-y-2">
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-2 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (projects.length === 0) {
    return (
      <Card className="p-12 text-center">
        <Folder className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">No projects found</h3>
        <p className="text-muted-foreground mb-4">
          Get started by creating your first project.
        </p>
        <div className="flex gap-2 justify-center">
          <Button onClick={onCreateProject} className="gap-2">
            <Plus className="w-4 h-4" />
            Create Project
          </Button>
          <Button onClick={onRefresh} variant="outline">
            Refresh
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {projects.map((project: any) => (
        <Card key={project.id} className="group hover:shadow-md transition-shadow">
          <CardHeader className="space-y-3">
            <div className="flex items-start justify-between">
              <div className="space-y-1 flex-1">
                <h3 className="font-semibold text-lg line-clamp-1 group-hover:text-primary transition-colors">
                  {project.name}
                </h3>
                <p className="text-sm text-muted-foreground font-mono">
                  {project.project_id}
                </p>
              </div>
              
              <Badge variant="outline" className={
                project.status === 'active' ? 'bg-green-500/10 text-green-600 border-green-200' :
                project.status === 'completed' ? 'bg-emerald-500/10 text-emerald-600 border-emerald-200' :
                project.status === 'planning' ? 'bg-blue-500/10 text-blue-600 border-blue-200' :
                'bg-muted text-muted-foreground'
              }>
                {project.status?.replace('_', ' ').toUpperCase()}
              </Badge>
            </div>

            {project.description && (
              <p className="text-sm text-muted-foreground line-clamp-2">
                {project.description}
              </p>
            )}
          </CardHeader>

          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground">
              Created: {new Date(project.created_at).toLocaleDateString()}
            </div>
            
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={() => navigate(`/projects/${project.id}`)}
            >
              View Project
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};