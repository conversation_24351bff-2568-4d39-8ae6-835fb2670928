import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Users } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface Project {
  id: string;
  name: string;
  teams?: {
    id: string;
    name: string;
    description?: string;
  };
}

interface ProjectTeamProps {
  project: Project;
}

export const ProjectTeam: React.FC<ProjectTeamProps> = ({ project }) => {
  const { data: teamMembers, isLoading } = useQuery({
    queryKey: ['projectTeamMembers', project.id],
    queryFn: async () => {
      if (!project.teams?.id) return [];

      // Get team members
      const { data: members, error: membersError } = await supabase
        .from('team_members')
        .select('user_id, role, joined_at')
        .eq('team_id', project.teams.id);

      if (membersError || !members || members.length === 0) return [];

      // Get profiles for team members
      const userIds = members.map(m => m.user_id);
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('user_id, first_name, last_name, email, avatar_url, position')
        .in('user_id', userIds);

      if (profilesError) return [];

      // Combine members with profiles
      return members.map(member => {
        const profile = profiles?.find(p => p.user_id === member.user_id);
        return {
          ...member,
          profile
        };
      });
    },
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Project Team</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4 text-muted-foreground">Loading team...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Project Team</CardTitle>
      </CardHeader>
      <CardContent>
        {project.teams ? (
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
              <Users className="w-4 h-4" />
              Team: {project.teams.name}
            </div>
            
            {teamMembers && teamMembers.length > 0 ? (
              <div className="space-y-3">
                {teamMembers.map((member) => (
                  <div key={member.user_id} className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                    <Avatar>
                      <AvatarFallback>
                        {member.profile?.first_name?.charAt(0)}
                        {member.profile?.last_name?.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="font-medium">
                        {member.profile?.first_name} {member.profile?.last_name}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {member.profile?.position || 'Team Member'}
                      </div>
                    </div>
                    <Badge variant={member.role === 'admin' ? 'default' : 'secondary'}>
                      {member.role}
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                No team members assigned to this project.
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <p>No team assigned to this project yet.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};