import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  Clock,
  Users,
  Calendar as CalendarIcon,
  Download,
  Target,
  AlertTriangle,
  CheckCircle,
  Activity
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthContext';
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns';
import { cn } from '@/lib/utils';

interface AdvancedReportsProps {
  className?: string;
}

export const AdvancedReports: React.FC<AdvancedReportsProps> = ({ className }) => {
  const [dateRange, setDateRange] = useState({
    from: startOfMonth(new Date()),
    to: endOfMonth(new Date()),
  });
  const [selectedMetric, setSelectedMetric] = useState('revenue');

  const { user } = useAuth();

  // Fetch comprehensive analytics data
  const { data: analytics, isLoading } = useQuery({
    queryKey: ['advanced-analytics', dateRange],
    queryFn: async () => {
      // Project completion rate analysis
      const { data: projects } = await (supabase as any)
        .from('projects')
        .select('id, status, created_at, end_date, budget')
        .gte('created_at', dateRange.from.toISOString())
        .lte('created_at', dateRange.to.toISOString());

      // Time tracking efficiency analysis
      const { data: timeEntries } = await (supabase as any)
        .from('time_entries')
        .select(`
          duration_minutes, 
          is_billable, 
          hourly_rate,
          project_id,
          projects(name, budget, status)
        `)
        .gte('start_time', dateRange.from.toISOString())
        .lte('start_time', dateRange.to.toISOString())
        .not('end_time', 'is', null);

      // Resource utilization analysis
      const { data: teamUtilization } = await (supabase as any)
        .from('time_entries')
        .select(`
          user_id,
          duration_minutes,
          profiles(first_name, last_name)
        `)
        .gte('start_time', dateRange.from.toISOString())
        .lte('start_time', dateRange.to.toISOString())
        .not('end_time', 'is', null);

      // Budget variance analysis
      const projectBudgetAnalysis = projects?.map(project => {
        const projectTime = timeEntries?.filter(entry => entry.project_id === project.id) || [];
        const totalSpent = projectTime.reduce((sum, entry) => {
          if (entry.is_billable && entry.hourly_rate) {
            return sum + ((entry.duration_minutes / 60) * entry.hourly_rate);
          }
          return sum;
        }, 0);
        
        const budgetUtilization = project.budget ? (totalSpent / project.budget) * 100 : 0;
        
        return {
          ...project,
          spent: totalSpent,
          budgetUtilization,
          isOverBudget: budgetUtilization > 100,
          hoursLogged: projectTime.reduce((sum, entry) => sum + (entry.duration_minutes / 60), 0),
        };
      }) || [];

      // Team productivity metrics
      interface TeamMember {
        name: string;
        totalHours: number;
        efficiency: number;
      }
      
      const teamMetrics = teamUtilization?.reduce((acc, entry) => {
        const userId = entry.user_id;
        if (!acc[userId]) {
          acc[userId] = {
            name: `${entry.profiles?.first_name} ${entry.profiles?.last_name}`,
            totalHours: 0,
            efficiency: 0,
          };
        }
        acc[userId].totalHours += entry.duration_minutes / 60;
        return acc;
      }, {} as Record<string, TeamMember>) || {};

      // Calculate efficiency scores (simplified - hours logged vs target)
      Object.keys(teamMetrics).forEach(userId => {
        const targetHours = 40; // Weekly target
        const actual = teamMetrics[userId].totalHours;
        teamMetrics[userId].efficiency = Math.min(100, Math.round((actual / targetHours) * 100));
      });

      // Revenue analysis
      const totalRevenue = timeEntries?.reduce((sum, entry) => {
        if (entry.is_billable && entry.hourly_rate) {
          return sum + ((entry.duration_minutes / 60) * entry.hourly_rate);
        }
        return sum;
      }, 0) || 0;

      const billableHours = timeEntries?.reduce((sum, entry) => {
        return sum + (entry.is_billable ? entry.duration_minutes / 60 : 0);
      }, 0) || 0;

      const totalHours = timeEntries?.reduce((sum, entry) => {
        return sum + (entry.duration_minutes / 60);
      }, 0) || 0;

      // Project completion predictions
      const projectPredictions = projectBudgetAnalysis.map(project => {
        const completionRate = project.status === 'completed' ? 100 : 
          project.status === 'in_progress' ? Math.min(90, project.budgetUtilization) : 
          project.budgetUtilization * 0.5;
        
        const riskLevel = project.isOverBudget ? 'high' : 
          project.budgetUtilization > 80 ? 'medium' : 'low';

        return {
          ...project,
          completionRate,
          riskLevel,
          predictedCompletion: completionRate >= 100 ? 'Completed' : 
            completionRate > 75 ? 'On Track' : 
            completionRate > 50 ? 'At Risk' : 'Behind Schedule'
        };
      });

      return {
        projects: projects || [],
        projectBudgetAnalysis,
        projectPredictions,
        teamMetrics: Object.values(teamMetrics),
        totalRevenue,
        billableHours,
        totalHours,
        billableRate: totalHours > 0 ? (billableHours / totalHours) * 100 : 0,
        avgHourlyRate: billableHours > 0 ? totalRevenue / billableHours : 0,
      };
    },
    enabled: !!user,
  });

  const exportReport = async () => {
    if (!analytics) return;

    const reportData = {
      period: `${format(dateRange.from, 'MMM dd, yyyy')} - ${format(dateRange.to, 'MMM dd, yyyy')}`,
      summary: {
        totalProjects: analytics.projects.length,
        totalRevenue: analytics.totalRevenue,
        billableHours: analytics.billableHours,
        billableRate: analytics.billableRate,
      },
      projectAnalysis: analytics.projectBudgetAnalysis,
      teamMetrics: analytics.teamMetrics,
      predictions: analytics.projectPredictions,
    };

    const csv = [
      ['RatioHub Advanced Analytics Report'],
      ['Generated on:', new Date().toISOString()],
      ['Period:', reportData.period],
      [''],
      ['SUMMARY METRICS'],
      ['Total Projects', reportData.summary.totalProjects],
      ['Total Revenue', `$${reportData.summary.totalRevenue.toFixed(2)}`],
      ['Billable Hours', reportData.summary.billableHours.toFixed(1)],
      ['Billable Rate', `${reportData.summary.billableRate.toFixed(1)}%`],
      [''],
      ['PROJECT ANALYSIS'],
      ['Project Name', 'Budget', 'Spent', 'Utilization %', 'Status', 'Risk Level'],
      ...analytics.projectBudgetAnalysis.map(p => [
        p.name || 'Unnamed Project',
        `$${p.budget || 0}`,
        `$${p.spent.toFixed(2)}`,
        `${p.budgetUtilization.toFixed(1)}%`,
        p.status,
        p.isOverBudget ? 'Over Budget' : 'On Track'
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `advanced-analytics-${format(new Date(), 'yyyy-MM-dd')}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="gap-2">
                <CalendarIcon className="w-4 h-4" />
                {format(dateRange.from, 'MMM d')} - {format(dateRange.to, 'MMM d')}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <Calendar
                mode="range"
                selected={dateRange}
                onSelect={(range) => {
                  if (range?.from && range?.to) {
                    setDateRange({ from: range.from, to: range.to });
                  }
                }}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
          
          <Select value={selectedMetric} onValueChange={setSelectedMetric}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Select metric" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="revenue">Revenue Focus</SelectItem>
              <SelectItem value="efficiency">Efficiency Focus</SelectItem>
              <SelectItem value="projects">Project Focus</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button onClick={exportReport} className="gap-2">
          <Download className="w-4 h-4" />
          Export Advanced Report
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${analytics?.totalRevenue.toFixed(2) || '0.00'}</div>
            <p className="text-xs text-muted-foreground">
              Avg ${analytics?.avgHourlyRate.toFixed(0) || '0'}/hr
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Billable Rate</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics?.billableRate.toFixed(1) || '0'}%</div>
            <p className="text-xs text-muted-foreground">
              {analytics?.billableHours.toFixed(1) || '0'}h billable
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics?.projects.filter(p => p.status === 'active').length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {analytics?.projects.length || 0} total projects
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Team Efficiency</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics?.teamMetrics.length > 0 
                ? Math.round((analytics.teamMetrics as any[]).reduce((sum: number, m: any) => sum + m.efficiency, 0) / analytics.teamMetrics.length)
                : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              {analytics?.teamMetrics.length || 0} team members
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Project Predictions & Budget Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              Project Completion Predictions
            </CardTitle>
            <CardDescription>
              AI-powered completion forecasts and risk analysis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics?.projectPredictions.slice(0, 5).map((project, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm">{project.name || 'Unnamed Project'}</span>
                      <Badge variant={
                        project.riskLevel === 'high' ? 'destructive' :
                        project.riskLevel === 'medium' ? 'default' : 'secondary'
                      }>
                        {project.riskLevel} risk
                      </Badge>
                    </div>
                    <span className="text-sm font-medium">{project.predictedCompletion}</span>
                  </div>
                  <Progress value={project.completionRate} className="h-2" />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>{project.completionRate.toFixed(1)}% complete</span>
                    <span>Budget: {project.budgetUtilization.toFixed(1)}% used</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Budget Variance Analysis
            </CardTitle>
            <CardDescription>
              Project budget utilization and variance tracking
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics?.projectBudgetAnalysis.slice(0, 5).map((project, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <div className="font-medium text-sm">{project.name || 'Unnamed Project'}</div>
                    <div className="text-xs text-muted-foreground">
                      {project.hoursLogged.toFixed(1)}h logged • ${project.spent.toFixed(0)} spent
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={cn(
                      "font-medium text-sm",
                      project.isOverBudget ? "text-destructive" : "text-foreground"
                    )}>
                      {project.budgetUtilization.toFixed(1)}%
                    </div>
                    <div className="text-xs text-muted-foreground">
                      of ${project.budget || 0} budget
                    </div>
                    {project.isOverBudget && (
                      <AlertTriangle className="w-4 h-4 text-destructive mt-1" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Team Performance Matrix */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Team Performance Matrix
          </CardTitle>
          <CardDescription>
            Individual productivity metrics and efficiency scores
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {(analytics?.teamMetrics as any[])?.map((member: any, index: number) => (
              <div key={index} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div className="font-medium">{member.name}</div>
                  <Badge variant={
                    member.efficiency >= 90 ? 'secondary' :
                    member.efficiency >= 70 ? 'default' : 'destructive'
                  }>
                    {member.efficiency}%
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Hours Logged</span>
                    <span className="font-medium">{member.totalHours.toFixed(1)}h</span>
                  </div>
                  <Progress value={member.efficiency} className="h-2" />
                  <div className="text-xs text-muted-foreground">
                    Efficiency vs target hours
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};