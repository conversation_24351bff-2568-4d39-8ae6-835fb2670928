import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Calendar as CalendarIcon, 
  Clock, 
  Users, 
  MapPin, 
  Plus,
  Filter,
  Download,
  Share2
} from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  type: 'task' | 'milestone' | 'meeting' | 'deadline';
  project_id?: string;
  project_name?: string;
  attendees?: string[];
  location?: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
}

interface CalendarView {
  type: 'month' | 'week' | 'day' | 'agenda';
  date: Date;
}

export const CalendarIntegration = () => {
  const [view, setView] = useState<CalendarView>({ type: 'month', date: new Date() });
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [filterProject, setFilterProject] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');

  const { data: events = [] } = useQuery({
    queryKey: ['calendar-events', view.date, filterProject, filterType],
    queryFn: async () => {
      // Mock calendar events data
      const today = new Date();
      const events: CalendarEvent[] = [
        {
          id: '1',
          title: 'Project Kickoff Meeting',
          description: 'Initial project planning and team introduction',
          start_time: new Date(today.getTime() + 24 * 60 * 60 * 1000).toISOString(),
          end_time: new Date(today.getTime() + 24 * 60 * 60 * 1000 + 60 * 60 * 1000).toISOString(),
          type: 'meeting',
          project_id: 'proj-1',
          project_name: 'Website Redesign',
          attendees: ['<EMAIL>', '<EMAIL>'],
          location: 'Conference Room A',
          status: 'scheduled'
        },
        {
          id: '2',
          title: 'Frontend Development Deadline',
          start_time: new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          end_time: new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          type: 'deadline',
          project_id: 'proj-1',
          project_name: 'Website Redesign',
          status: 'scheduled'
        },
        {
          id: '3',
          title: 'Phase 1 Milestone',
          description: 'Complete wireframes and design mockups',
          start_time: new Date(today.getTime() + 14 * 24 * 60 * 60 * 1000).toISOString(),
          end_time: new Date(today.getTime() + 14 * 24 * 60 * 60 * 1000).toISOString(),
          type: 'milestone',
          project_id: 'proj-1',
          project_name: 'Website Redesign',
          status: 'scheduled'
        },
        {
          id: '4',
          title: 'API Development Task',
          start_time: new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000).toISOString(),
          end_time: new Date(today.getTime() + 5 * 24 * 60 * 60 * 1000).toISOString(),
          type: 'task',
          project_id: 'proj-2',
          project_name: 'Mobile App',
          status: 'in_progress'
        }
      ];

      return events;
    }
  });

  const { data: projects = [] } = useQuery({
    queryKey: ['projects-for-calendar'],
    queryFn: async () => {
      return [
        { id: 'proj-1', name: 'Website Redesign' },
        { id: 'proj-2', name: 'Mobile App' },
        { id: 'proj-3', name: 'Database Migration' }
      ];
    }
  });

  const getEventTypeColor = (type: CalendarEvent['type']) => {
    switch (type) {
      case 'task': return 'bg-blue-500';
      case 'milestone': return 'bg-green-500';
      case 'meeting': return 'bg-purple-500';
      case 'deadline': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getEventTypeBadgeVariant = (type: CalendarEvent['type']) => {
    switch (type) {
      case 'task': return 'default';
      case 'milestone': return 'secondary';
      case 'meeting': return 'outline';
      case 'deadline': return 'destructive';
      default: return 'outline';
    }
  };

  const getStatusColor = (status: CalendarEvent['status']) => {
    switch (status) {
      case 'completed': return 'text-green-600';
      case 'in_progress': return 'text-blue-600';
      case 'cancelled': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const filteredEvents = events.filter(event => {
    if (filterProject !== 'all' && event.project_id !== filterProject) return false;
    if (filterType !== 'all' && event.type !== filterType) return false;
    return true;
  });

  const eventsForSelectedDate = selectedDate 
    ? filteredEvents.filter(event => {
        const eventDate = new Date(event.start_time);
        return eventDate.toDateString() === selectedDate.toDateString();
      })
    : [];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">Calendar & Schedule</h2>
        <div className="flex gap-4">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Event
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline">
            <Share2 className="h-4 w-4 mr-2" />
            Sync Calendar
          </Button>
        </div>
      </div>

      <div className="flex gap-4">
        <Select value={filterProject} onValueChange={setFilterProject}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by project" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Projects</SelectItem>
            {projects.map(project => (
              <SelectItem key={project.id} value={project.id}>{project.name}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="task">Tasks</SelectItem>
            <SelectItem value="milestone">Milestones</SelectItem>
            <SelectItem value="meeting">Meetings</SelectItem>
            <SelectItem value="deadline">Deadlines</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Tabs value={view.type} onValueChange={(value) => setView(prev => ({ ...prev, type: value as any }))}>
        <TabsList>
          <TabsTrigger value="month">Month</TabsTrigger>
          <TabsTrigger value="week">Week</TabsTrigger>
          <TabsTrigger value="day">Day</TabsTrigger>
          <TabsTrigger value="agenda">Agenda</TabsTrigger>
        </TabsList>

        <TabsContent value="month" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-3">
            <div className="md:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Calendar View</span>
                    <div className="flex gap-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setView(prev => ({ ...prev, date: new Date(prev.date.getFullYear(), prev.date.getMonth() - 1) }))}
                      >
                        Previous
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setView(prev => ({ ...prev, date: new Date(prev.date.getFullYear(), prev.date.getMonth() + 1) }))}
                      >
                        Next
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={setSelectedDate}
                    className="rounded-md border"
                  />
                </CardContent>
              </Card>
            </div>

            <div>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CalendarIcon className="h-5 w-5" />
                    {selectedDate ? format(selectedDate, 'MMMM d, yyyy') : 'Select a date'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {eventsForSelectedDate.length === 0 ? (
                    <p className="text-sm text-muted-foreground">No events for this date</p>
                  ) : (
                    <div className="space-y-3">
                      {eventsForSelectedDate.map(event => (
                        <div key={event.id} className="space-y-2 p-3 border rounded-lg">
                          <div className="flex items-center justify-between">
                            <p className="font-medium text-sm">{event.title}</p>
                            <Badge variant={getEventTypeBadgeVariant(event.type)}>
                              {event.type}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            <span>
                              {format(new Date(event.start_time), 'h:mm a')}
                              {event.end_time && ` - ${format(new Date(event.end_time), 'h:mm a')}`}
                            </span>
                          </div>
                          {event.project_name && (
                            <div className="text-xs text-muted-foreground">
                              {event.project_name}
                            </div>
                          )}
                          {event.location && (
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <MapPin className="h-3 w-3" />
                              <span>{event.location}</span>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="agenda" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Events</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredEvents.map(event => (
                  <div key={event.id} className="flex items-center gap-4 p-4 border rounded-lg">
                    <div className={cn("w-3 h-3 rounded-full", getEventTypeColor(event.type))} />
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center justify-between">
                        <p className="font-medium">{event.title}</p>
                        <div className="flex items-center gap-2">
                          <Badge variant={getEventTypeBadgeVariant(event.type)}>
                            {event.type}
                          </Badge>
                          <span className={cn("text-sm", getStatusColor(event.status))}>
                            {event.status}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <CalendarIcon className="h-3 w-3" />
                          <span>{format(new Date(event.start_time), 'MMM d, yyyy')}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>{format(new Date(event.start_time), 'h:mm a')}</span>
                        </div>
                        {event.project_name && (
                          <span>• {event.project_name}</span>
                        )}
                      </div>
                      {event.description && (
                        <p className="text-sm text-muted-foreground">{event.description}</p>
                      )}
                      {event.attendees && event.attendees.length > 0 && (
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <Users className="h-3 w-3" />
                          <span>{event.attendees.length} attendees</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="week">
          <Card>
            <CardHeader>
              <CardTitle>Week View</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Week view coming soon...
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="day">
          <Card>
            <CardHeader>
              <CardTitle>Day View</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Day view coming soon...
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};