import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Bell, CheckCircle, Info, AlertTriangle, Settings, Mail } from 'lucide-react';
import { format } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// Interface definitions
interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  user_id: string;
  read: boolean;
  created_at: string;
  action_url?: string;
}

interface NotificationSettings {
  email_enabled: boolean;
  push_enabled: boolean;
  project_updates: boolean;
  task_assignments: boolean;
  deadlines: boolean;
  mentions: boolean;
}

const defaultSettings: NotificationSettings = {
  email_enabled: true,
  push_enabled: true,
  project_updates: true,
  task_assignments: true,
  deadlines: true,
  mentions: true,
};

export const NotificationCenter: React.FC = () => {
  const queryClient = useQueryClient();

  // Fetch notifications from the database
  const { data: notifications = [], refetch } = useQuery({
    queryKey: ['notifications'],
    queryFn: async () => {
      try {
        const { data: user } = await supabase.auth.getUser();
        if (!user.user) return [];

        const { data: dbNotifications, error } = await (supabase as any)
          .from('notifications')
          .select('*')
          .eq('user_id', user.user.id)
          .order('created_at', { ascending: false })
          .limit(50);

        if (error) throw error;
        return dbNotifications || [];
      } catch (error) {
        console.error('Error fetching notifications:', error);
        return [];
      }
    }
  });

  // Fetch notification settings
  const { data: settings, refetch: refetchSettings } = useQuery({
    queryKey: ['notification-settings'],
    queryFn: async () => {
      try {
        const { data: user } = await supabase.auth.getUser();
        if (!user.user) return defaultSettings;

        const { data, error } = await (supabase as any)
          .from('notification_settings')
          .select('*')
          .eq('user_id', user.user.id)
          .maybeSingle();

        if (error && error.code !== 'PGRST116') throw error;
        return data || defaultSettings;
      } catch (error) {
        console.error('Error fetching notification settings:', error);
        return defaultSettings;
      }
    }
  });

  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>(defaultSettings);

  // Update local state when settings are fetched
  useEffect(() => {
    if (settings) {
      setNotificationSettings(settings);
    }
  }, [settings]);

  const markAsReadMutation = useMutation({
    mutationFn: async (notificationId: string) => {
      const { error } = await (supabase as any)
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId);

      if (error) throw error;
      return notificationId;
    },
    onSuccess: () => {
      toast.success('Notification marked as read');
      refetch();
    },
    onError: () => {
      toast.error('Failed to mark notification as read');
    }
  });

  const updateSettingsMutation = useMutation({
    mutationFn: async (newSettings: NotificationSettings) => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('User not authenticated');

      const { error } = await (supabase as any)
        .from('notification_settings')
        .upsert({
          user_id: user.user.id,
          ...newSettings
        });

      if (error) throw error;
      return newSettings;
    },
    onSuccess: (newSettings) => {
      setNotificationSettings(newSettings);
      refetchSettings();
      toast.success('Notification settings updated');
    },
    onError: () => {
      toast.error('Failed to update settings');
    }
  });

  const sendTestNotificationMutation = useMutation({
    mutationFn: async () => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('User not authenticated');

      const { error } = await (supabase as any)
        .from('notifications')
        .insert({
          user_id: user.user.id,
          title: 'Test Notification',
          message: 'This is a test notification to verify the system is working.',
          type: 'info'
        });

      if (error) throw error;
    },
    onSuccess: () => {
      toast.success('Test notification sent');
      refetch();
    },
    onError: () => {
      toast.error('Failed to send test notification');
    }
  });

  // Request notification permission on mount
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default: return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  const getNotificationBadgeVariant = (type: string) => {
    switch (type) {
      case 'success': return 'default';
      case 'warning': return 'secondary';
      case 'error': return 'destructive';
      default: return 'outline';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">Notifications</h2>
        <Button 
          onClick={() => sendTestNotificationMutation.mutate()}
          disabled={sendTestNotificationMutation.isPending}
          variant="outline"
        >
          <Bell className="h-4 w-4 mr-2" />
          Send Test
        </Button>
      </div>

      <Tabs defaultValue="notifications" className="space-y-4">
        <TabsList>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="templates">Email Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Notifications</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {notifications.length > 0 ? (
                  notifications.map((notification: Notification) => (
                    <div
                      key={notification.id}
                      className={`flex items-start justify-between p-4 border rounded-lg ${
                        notification.read ? 'bg-muted/30' : 'bg-background'
                      }`}
                    >
                      <div className="flex items-start gap-3 flex-1">
                        {getNotificationIcon(notification.type)}
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center gap-2">
                            <p className="font-medium">{notification.title}</p>
                            <Badge variant={getNotificationBadgeVariant(notification.type)}>
                              {notification.type}
                            </Badge>
                            {!notification.read && (
                              <Badge variant="destructive" className="text-xs">
                                New
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {notification.message}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {format(new Date(notification.created_at), 'MMM dd, yyyy h:mm a')}
                          </p>
                        </div>
                      </div>
                      {!notification.read && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => markAsReadMutation.mutate(notification.id)}
                          disabled={markAsReadMutation.isPending}
                        >
                          Mark as Read
                        </Button>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Bell className="mx-auto h-12 w-12 mb-4 opacity-50" />
                    <p>No notifications yet</p>
                    <p className="text-sm">You'll see important updates here</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Notification Preferences
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="email">Email Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive notifications via email
                    </p>
                  </div>
                  <Switch
                    id="email"
                    checked={notificationSettings.email_enabled}
                    onCheckedChange={(checked) =>
                      setNotificationSettings(prev => ({ ...prev, email_enabled: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="push">Push Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive push notifications in your browser
                    </p>
                  </div>
                  <Switch
                    id="push"
                    checked={notificationSettings.push_enabled}
                    onCheckedChange={(checked) =>
                      setNotificationSettings(prev => ({ ...prev, push_enabled: checked }))
                    }
                  />
                </div>

                <div className="border-t pt-4">
                  <h4 className="font-medium mb-4">Notification Types</h4>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label htmlFor="project-updates">Project Updates</Label>
                        <p className="text-sm text-muted-foreground">
                          Get notified about project status changes
                        </p>
                      </div>
                      <Switch
                        id="project-updates"
                        checked={notificationSettings.project_updates}
                        onCheckedChange={(checked) =>
                          setNotificationSettings(prev => ({ ...prev, project_updates: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label htmlFor="task-assignments">Task Assignments</Label>
                        <p className="text-sm text-muted-foreground">
                          Get notified when tasks are assigned to you
                        </p>
                      </div>
                      <Switch
                        id="task-assignments"
                        checked={notificationSettings.task_assignments}
                        onCheckedChange={(checked) =>
                          setNotificationSettings(prev => ({ ...prev, task_assignments: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label htmlFor="deadlines">Deadline Reminders</Label>
                        <p className="text-sm text-muted-foreground">
                          Get reminded about upcoming deadlines
                        </p>
                      </div>
                      <Switch
                        id="deadlines"
                        checked={notificationSettings.deadlines}
                        onCheckedChange={(checked) =>
                          setNotificationSettings(prev => ({ ...prev, deadlines: checked }))
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label htmlFor="mentions">Mentions</Label>
                        <p className="text-sm text-muted-foreground">
                          Get notified when someone mentions you
                        </p>
                      </div>
                      <Switch
                        id="mentions"
                        checked={notificationSettings.mentions}
                        onCheckedChange={(checked) =>
                          setNotificationSettings(prev => ({ ...prev, mentions: checked }))
                        }
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  onClick={() => updateSettingsMutation.mutate(notificationSettings)}
                  disabled={updateSettingsMutation.isPending}
                >
                  Save Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Email Templates
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Mail className="mx-auto h-12 w-12 mb-4 opacity-50" />
                <p>Email template management is available in system configuration</p>
                <p className="text-sm">Contact your administrator to customize email templates</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};