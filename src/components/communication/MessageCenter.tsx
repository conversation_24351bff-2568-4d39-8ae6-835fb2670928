import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  MessageCircle, 
  Send, 
  Search, 
  Bell,
  Users,
  Filter,
  Archive,
  Star,
  Reply,
  UserPlus
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthContext';
import { format } from 'date-fns';
import { toast } from 'sonner';

interface Message {
  id: string;
  subject: string;
  content: string;
  sender_id: string;
  sender_name: string;
  recipient_id?: string;
  recipient_name?: string;
  project_id?: string;
  project_name?: string;
  is_read: boolean;
  priority: 'low' | 'medium' | 'high';
  created_at: string;
  message_type: 'direct' | 'project' | 'system';
}

export const MessageCenter: React.FC = () => {
  const [activeTab, setActiveTab] = useState('inbox');
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [newMessageData, setNewMessageData] = useState<{
    recipient_id: string;
    subject: string;
    content: string;
    priority: 'low' | 'medium' | 'high';
  }>({
    recipient_id: '',
    subject: '',
    content: '',
    priority: 'medium',
  });
  const [searchQuery, setSearchQuery] = useState('');

  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Fetch messages
  const { data: messages = [], isLoading } = useQuery({
    queryKey: ['messages', activeTab, user?.id],
    queryFn: async () => {
      const query = (supabase as any)
        .from('messages')
        .select(`
          *,
          sender:profiles!messages_sender_id_fkey(first_name, last_name),
          recipient:profiles!messages_recipient_id_fkey(first_name, last_name),
          project:projects(name)
        `)
        .order('created_at', { ascending: false });

      if (activeTab === 'inbox') {
        query.eq('recipient_id', user?.id);
      } else if (activeTab === 'sent') {
        query.eq('sender_id', user?.id);
      }

      const { data } = await query;
      
      return data?.map((msg: any) => ({
        ...msg,
        sender_name: `${msg.sender?.first_name || ''} ${msg.sender?.last_name || ''}`.trim(),
        recipient_name: `${msg.recipient?.first_name || ''} ${msg.recipient?.last_name || ''}`.trim(),
        project_name: msg.project?.name,
      })) || [];
    },
    enabled: !!user,
  });

  // Fetch team members for new message
  const { data: teamMembers = [] } = useQuery({
    queryKey: ['team-members'],
    queryFn: async () => {
      const { data } = await (supabase as any)
        .from('profiles')
        .select('user_id, first_name, last_name, role')
        .neq('user_id', user?.id);
      
      return data?.map((member: any) => ({
        ...member,
        full_name: `${member.first_name || ''} ${member.last_name || ''}`.trim(),
      })) || [];
    },
    enabled: !!user,
  });

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: async (messageData: any) => {
      const { data, error } = await (supabase as any)
        .from('messages')
        .insert({
          ...messageData,
          sender_id: user?.id,
          message_type: 'direct',
          is_read: false,
        });
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['messages'] });
      setNewMessageData({
        recipient_id: '',
        subject: '',
        content: '',
        priority: 'medium',
      });
      toast.success('Message sent successfully');
    },
    onError: () => {
      toast.error('Failed to send message');
    },
  });

  // Reply to message mutation
  const replyMutation = useMutation({
    mutationFn: async (content: string) => {
      if (!selectedMessage) return;
      
      const { data, error } = await (supabase as any)
        .from('messages')
        .insert({
          subject: `Re: ${selectedMessage.subject}`,
          content,
          sender_id: user?.id,
          recipient_id: selectedMessage.sender_id,
          priority: selectedMessage.priority,
          message_type: 'direct',
          is_read: false,
        });
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['messages'] });
      setReplyContent('');
      toast.success('Reply sent successfully');
    },
    onError: () => {
      toast.error('Failed to send reply');
    },
  });

  // Mark as read mutation
  const markAsReadMutation = useMutation({
    mutationFn: async (messageId: string) => {
      const { error } = await (supabase as any)
        .from('messages')
        .update({ is_read: true })
        .eq('id', messageId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['messages'] });
    },
  });

  const handleSendMessage = () => {
    if (!newMessageData.recipient_id || !newMessageData.subject || !newMessageData.content) {
      toast.error('Please fill in all required fields');
      return;
    }
    sendMessageMutation.mutate(newMessageData);
  };

  const handleReply = () => {
    if (!replyContent.trim()) {
      toast.error('Please enter a reply message');
      return;
    }
    replyMutation.mutate(replyContent);
  };

  const handleSelectMessage = (message: Message) => {
    setSelectedMessage(message);
    if (!message.is_read && message.recipient_id === user?.id) {
      markAsReadMutation.mutate(message.id);
    }
  };

  const filteredMessages = messages.filter(message =>
    message.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
    message.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
    message.sender_name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const unreadCount = messages.filter(msg => !msg.is_read && msg.recipient_id === user?.id).length;

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-destructive';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-muted-foreground';
      default: return 'text-muted-foreground';
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[800px]">
      {/* Message List */}
      <div className="lg:col-span-1 space-y-4">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <MessageCircle className="w-5 h-5" />
                Messages
                {unreadCount > 0 && (
                  <Badge variant="destructive" className="ml-2">
                    {unreadCount}
                  </Badge>
                )}
              </CardTitle>
              <Button
                size="sm"
                onClick={() => setSelectedMessage(null)}
                className="gap-2"
              >
                <UserPlus className="w-4 h-4" />
                New
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search messages..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>

            {/* Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="inbox">Inbox</TabsTrigger>
                <TabsTrigger value="sent">Sent</TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Message List */}
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {filteredMessages.map((message) => (
                <div
                  key={message.id}
                  onClick={() => handleSelectMessage(message)}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors hover:bg-accent ${
                    selectedMessage?.id === message.id ? 'bg-accent' : ''
                  } ${!message.is_read && message.recipient_id === user?.id ? 'border-primary' : ''}`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Avatar className="w-6 h-6">
                        <AvatarFallback className="text-xs">
                          {(activeTab === 'inbox' ? message.sender_name : message.recipient_name)?.charAt(0) || 'U'}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm font-medium">
                        {activeTab === 'inbox' ? message.sender_name : message.recipient_name}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <span className={`text-xs ${getPriorityColor(message.priority)}`}>
                        {message.priority}
                      </span>
                      {!message.is_read && message.recipient_id === user?.id && (
                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                      )}
                    </div>
                  </div>
                  <div className="text-sm font-medium mb-1 truncate">
                    {message.subject}
                  </div>
                  <div className="text-xs text-muted-foreground truncate mb-1">
                    {message.content}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {format(new Date(message.created_at), 'MMM d, h:mm a')}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Message Content/Compose */}
      <div className="lg:col-span-2">
        {selectedMessage ? (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>{selectedMessage.subject}</CardTitle>
                  <CardDescription>
                    From: {selectedMessage.sender_name} • {format(new Date(selectedMessage.created_at), 'MMM d, yyyy h:mm a')}
                  </CardDescription>
                </div>
                <Badge variant={selectedMessage.priority === 'high' ? 'destructive' : 'default'}>
                  {selectedMessage.priority} priority
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="prose prose-sm max-w-none">
                <div className="whitespace-pre-wrap">{selectedMessage.content}</div>
              </div>

              {/* Reply Section */}
              <div className="border-t pt-4 space-y-4">
                <h4 className="font-medium flex items-center gap-2">
                  <Reply className="w-4 h-4" />
                  Reply
                </h4>
                <Textarea
                  placeholder="Type your reply..."
                  value={replyContent}
                  onChange={(e) => setReplyContent(e.target.value)}
                  rows={4}
                />
                <Button
                  onClick={handleReply}
                  disabled={replyMutation.isPending || !replyContent.trim()}
                  className="gap-2"
                >
                  <Send className="w-4 h-4" />
                  Send Reply
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>New Message</CardTitle>
              <CardDescription>
                Send a message to team members
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">To</label>
                <Select
                  value={newMessageData.recipient_id}
                  onValueChange={(value) => setNewMessageData(prev => ({ ...prev, recipient_id: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select recipient" />
                  </SelectTrigger>
                  <SelectContent>
                    {teamMembers.map((member) => (
                      <SelectItem key={member.user_id} value={member.user_id}>
                        {member.full_name} ({member.role})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Subject</label>
                <Input
                  placeholder="Message subject"
                  value={newMessageData.subject}
                  onChange={(e) => setNewMessageData(prev => ({ ...prev, subject: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Priority</label>
                <Select
                  value={newMessageData.priority}
                  onValueChange={(value: 'low' | 'medium' | 'high') => setNewMessageData(prev => ({ ...prev, priority: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low Priority</SelectItem>
                    <SelectItem value="medium">Medium Priority</SelectItem>
                    <SelectItem value="high">High Priority</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Message</label>
                <Textarea
                  placeholder="Type your message here..."
                  value={newMessageData.content}
                  onChange={(e) => setNewMessageData(prev => ({ ...prev, content: e.target.value }))}
                  rows={6}
                />
              </div>

              <Button
                onClick={handleSendMessage}
                disabled={sendMessageMutation.isPending}
                className="gap-2"
              >
                <Send className="w-4 h-4" />
                Send Message
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};