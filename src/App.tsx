import React from 'react';
import { Browser<PERSON>outer, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from 'next-themes';
import { Toaster } from '@/components/ui/sonner';
import { AuthProvider } from '@/components/auth/AuthContext';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

// Pages
import Index from '@/pages/Index';
import Auth from '@/pages/Auth';
import Dashboard from '@/pages/Dashboard';
import Projects from '@/pages/Projects';
import ProjectDetails from '@/pages/ProjectDetails';
import ProjectDetailsSimple from '@/pages/ProjectDetailsSimple';
import Teams from '@/pages/Teams';
import TeamDetails from '@/pages/TeamDetails';
import TimeTracking from '@/pages/TimeTracking';
import SupportTickets from '@/pages/SupportTickets';
import Settings from '@/pages/Settings';
import Reports from '@/pages/Reports';
import Admin from '@/pages/Admin';
import ClientPortal from '@/pages/ClientPortal';
import Communication from '@/pages/Communication';
import Notifications from '@/pages/Notifications';
import Billing from '@/pages/Billing';
import Calendar from '@/pages/Calendar';
import NotFound from '@/pages/NotFound';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: false,
    },
  },
});

function App() {
  return (
    <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <BrowserRouter>
            <Routes>
              <Route path="/auth" element={<Auth />} />
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              <Route 
                path="/dashboard" 
                element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/projects" 
                element={
                  <ProtectedRoute>
                    <Projects />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/projects/:id" 
                element={
                  <ProtectedRoute>
                    <ProjectDetails />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/projects-simple/:id" 
                element={
                  <ProtectedRoute>
                    <ProjectDetailsSimple />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/teams" 
                element={
                  <ProtectedRoute allowedRoles={['admin', 'project_manager']}>
                    <Teams />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/teams/:id" 
                element={
                  <ProtectedRoute allowedRoles={['admin', 'project_manager']}>
                    <TeamDetails />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/time-tracking"
                element={
                  <ProtectedRoute>
                    <TimeTracking />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/support-tickets" 
                element={
                  <ProtectedRoute>
                    <SupportTickets />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/reports" 
                element={
                  <ProtectedRoute allowedRoles={['admin', 'project_manager']}>
                    <Reports />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/settings" 
                element={
                  <ProtectedRoute>
                    <Settings />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/admin" 
                element={
                  <ProtectedRoute requiredRole="admin">
                    <Admin />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/client-portal" 
                element={
                  <ProtectedRoute>
                    <ClientPortal />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/communication" 
                element={
                  <ProtectedRoute>
                    <Communication />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/notifications" 
                element={
                  <ProtectedRoute>
                    <Notifications />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/billing" 
                element={
                  <ProtectedRoute allowedRoles={['admin', 'project_manager']}>
                    <Billing />
                  </ProtectedRoute>
                } 
              />
              <Route 
                path="/calendar" 
                element={
                  <ProtectedRoute>
                    <Calendar />
                  </ProtectedRoute>
                } 
              />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
          <Toaster />
        </AuthProvider>
      </QueryClientProvider>
    </ThemeProvider>
  );
}

export default App;