// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://rgcxrksmdkzarcdlscpk.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJnY3hya3NtZGt6YXJjZGxzY3BrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ5ODU3MzksImV4cCI6MjA3MDU2MTczOX0.IwswTBZoHYh_WCdVO2RgEZIxYVr9-aYAwLqGAS647YA";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});