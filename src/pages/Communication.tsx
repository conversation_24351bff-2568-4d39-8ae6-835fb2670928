import React from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { MessageCenter } from '@/components/communication/MessageCenter';
import { WorkflowAutomation } from '@/components/workflow/WorkflowAutomation';
import { MessageCircle, Workflow, Bell, Calendar } from 'lucide-react';

const Communication: React.FC = () => {
  return (
    <DashboardLayout>
      <div className="container mx-auto p-6 space-y-8">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Communication & Workflow</h1>
          <p className="text-muted-foreground">
            Manage team communication and automate project workflows
          </p>
        </div>

        <Tabs defaultValue="messages" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="messages" className="flex items-center gap-2">
              <MessageCircle className="w-4 h-4" />
              Message Center
            </TabsTrigger>
            <TabsTrigger value="workflow" className="flex items-center gap-2">
              <Workflow className="w-4 h-4" />
              Workflow Automation
            </TabsTrigger>
          </TabsList>

          <TabsContent value="messages">
            <MessageCenter />
          </TabsContent>

          <TabsContent value="workflow">
            <WorkflowAutomation />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default Communication;