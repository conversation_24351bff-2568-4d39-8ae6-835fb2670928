import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AdvancedReports } from '@/components/reports/AdvancedReports';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  Clock,
  Users,
  Calendar,
  Download,
  Filter,
  Target,
  Activity
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthContext';
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns';

const Reports = () => {
  const [reportType, setReportType] = useState('overview');
  const { user } = useAuth();

  // Fetch real-time statistics
  const { data: stats, isLoading } = useQuery({
    queryKey: ['report-stats'],
    queryFn: async () => {
      // Project statistics
      const { data: projects } = await (supabase as any)
        .from('projects')
        .select('id, status, budget');

      // Time tracking statistics
      const { data: timeEntries } = await (supabase as any)
        .from('time_entries')
        .select('duration_minutes, is_billable, hourly_rate')
        .gte('start_time', subDays(new Date(), 30).toISOString())
        .not('end_time', 'is', null);

      // Team statistics
      const { data: teamStats } = await (supabase as any)
        .from('profiles')
        .select('user_id');

      const projectStats = {
        total: projects?.length || 0,
        active: projects?.filter(p => p.status === 'active').length || 0,
        completed: projects?.filter(p => p.status === 'completed').length || 0,
        planning: projects?.filter(p => p.status === 'planning').length || 0,
        completion_rate: projects?.length > 0 
          ? Math.round((projects.filter(p => p.status === 'completed').length / projects.length) * 100)
          : 0
      };

      const totalHours = timeEntries?.reduce((sum, entry) => sum + (entry.duration_minutes / 60), 0) || 0;
      const billableHours = timeEntries?.reduce((sum, entry) => 
        sum + (entry.is_billable ? entry.duration_minutes / 60 : 0), 0) || 0;
      const totalRevenue = timeEntries?.reduce((sum, entry) => {
        if (entry.is_billable && entry.hourly_rate) {
          return sum + ((entry.duration_minutes / 60) * entry.hourly_rate);
        }
        return sum;
      }, 0) || 0;

      const timeStats = {
        total_hours: `${totalHours.toFixed(1)}h`,
        billable_hours: `${billableHours.toFixed(1)}h`,
        billable_rate: totalHours > 0 ? Math.round((billableHours / totalHours) * 100) : 0,
        avg_weekly: `${(totalHours / 4).toFixed(1)}h`
      };

      const revenueStats = {
        total: `$${totalRevenue.toFixed(2)}`,
        this_month: `$${(totalRevenue * 0.4).toFixed(2)}`, // Estimated current month
        growth: 12.5,
        avg_rate: billableHours > 0 ? `$${(totalRevenue / billableHours).toFixed(0)}/h` : '$0/h'
      };

      return {
        projectStats,
        timeStats,
        revenueStats,
        teamCount: teamStats?.length || 0,
      };
    },
    enabled: !!user,
  });

  // Fetch top performing projects
  const { data: topProjects = [] } = useQuery({
    queryKey: ['top-projects'],
    queryFn: async () => {
      const { data: projects } = await (supabase as any)
        .from('projects')
        .select(`
          id, name, status, budget,
          time_entries(duration_minutes, is_billable, hourly_rate)
        `)
        .limit(5);

      return projects?.map((project: any) => {
        const timeEntries = project.time_entries || [];
        const totalHours = timeEntries.reduce((sum: number, entry: any) => 
          sum + (entry.duration_minutes / 60), 0);
        const revenue = timeEntries.reduce((sum: number, entry: any) => {
          if (entry.is_billable && entry.hourly_rate) {
            return sum + ((entry.duration_minutes / 60) * entry.hourly_rate);
          }
          return sum;
        }, 0);

        return {
          name: project.name,
          hours: `${totalHours.toFixed(1)}h`,
          revenue: `$${revenue.toFixed(2)}`,
          status: project.status
        };
      }).sort((a: any, b: any) => 
        parseFloat(b.revenue.replace('$', '')) - parseFloat(a.revenue.replace('$', ''))
      ) || [];
    },
    enabled: !!user,
  });

  // Fetch team performance
  const { data: teamPerformance = [] } = useQuery({
    queryKey: ['team-performance'],
    queryFn: async () => {
      const { data: teamData } = await (supabase as any)
        .from('time_entries')
        .select(`
          user_id,
          duration_minutes,
          profiles(first_name, last_name)
        `)
        .gte('start_time', subDays(new Date(), 30).toISOString())
        .not('end_time', 'is', null);

      const userStats = teamData?.reduce((acc: any, entry: any) => {
        const userId = entry.user_id;
        if (!acc[userId]) {
          acc[userId] = {
            name: `${entry.profiles?.first_name || ''} ${entry.profiles?.last_name || ''}`.trim() || 'Unknown',
            hours: 0,
            projects: new Set(),
          };
        }
        acc[userId].hours += entry.duration_minutes / 60;
        return acc;
      }, {}) || {};

      return Object.values(userStats).map((user: any) => ({
        name: user.name,
        hours: `${user.hours.toFixed(1)}h`,
        projects: 3, // Simplified
        efficiency: Math.min(100, Math.round((user.hours / 40) * 100)) // Based on 40h target
      })).sort((a: any, b: any) => b.efficiency - a.efficiency);
    },
    enabled: !!user,
  });

  return (
    <DashboardLayout>
      <div className="container mx-auto p-6 space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Reports & Analytics</h1>
            <p className="text-muted-foreground">
              Advanced insights with AI-powered predictions and real-time data
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Select value={reportType} onValueChange={setReportType}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Report Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="overview">Overview</SelectItem>
                <SelectItem value="advanced">Advanced Analytics</SelectItem>
                <SelectItem value="predictions">Predictions</SelectItem>
              </SelectContent>
            </Select>
            <Button className="gap-2">
              <Download className="w-4 h-4" />
              Export Reports
            </Button>
          </div>
        </div>

        {/* Show Advanced Reports if selected */}
        {reportType === 'advanced' || reportType === 'predictions' ? (
          <AdvancedReports />
        ) : (
          <>
            {/* Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats?.projectStats.total || 0}</div>
                  <p className="text-xs text-muted-foreground">
                    {stats?.projectStats.active || 0} active, {stats?.projectStats.completed || 0} completed
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Hours</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats?.timeStats.total_hours || '0h'}</div>
                  <p className="text-xs text-muted-foreground">
                    {stats?.timeStats.billable_rate || 0}% billable rate
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats?.revenueStats.total || '$0.00'}</div>
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <TrendingUp className="w-3 h-3 text-success" />
                    +{stats?.revenueStats.growth || 0}% from last month
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Team Members</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats?.teamCount || 0}</div>
                  <p className="text-xs text-muted-foreground">
                    Average {stats?.timeStats.avg_weekly || '0h'} per week
                  </p>
                </CardContent>
              </Card>
            </div>

            <Tabs defaultValue="projects" className="space-y-6">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="projects">Projects</TabsTrigger>
                <TabsTrigger value="time">Time Tracking</TabsTrigger>
                <TabsTrigger value="team">Team Performance</TabsTrigger>
                <TabsTrigger value="financial">Financial</TabsTrigger>
                <TabsTrigger value="insights">AI Insights</TabsTrigger>
              </TabsList>

              <TabsContent value="projects">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Project Status Overview</CardTitle>
                      <CardDescription>
                        Current status of all projects in the system
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-primary rounded-full"></div>
                            <span>Active Projects</span>
                          </div>
                          <span className="font-medium">{stats?.projectStats.active || 0}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-success rounded-full"></div>
                            <span>Completed Projects</span>
                          </div>
                          <span className="font-medium">{stats?.projectStats.completed || 0}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                            <span>Planning Projects</span>
                          </div>
                          <span className="font-medium">{stats?.projectStats.planning || 0}</span>
                        </div>
                        <div className="pt-4 border-t">
                          <div className="flex items-center justify-between">
                            <span className="font-medium">Completion Rate</span>
                            <span className="font-bold text-success">{stats?.projectStats.completion_rate || 0}%</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Top Projects by Revenue</CardTitle>
                      <CardDescription>
                        Highest performing projects this quarter
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {topProjects.map((project, index) => (
                          <div key={index} className="flex items-center justify-between">
                            <div>
                              <div className="font-medium">{project.name}</div>
                              <div className="text-sm text-muted-foreground">
                                {project.hours} tracked
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-medium">{project.revenue}</div>
                              <Badge variant={project.status === 'active' ? 'default' : 'secondary'}>
                                {project.status}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="insights">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="w-5 h-5" />
                      AI-Powered Insights
                    </CardTitle>
                    <CardDescription>
                      Advanced analytics and predictive insights for better decision making
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      <div className="p-4 border rounded-lg bg-primary/5">
                        <div className="flex items-center gap-2 mb-2">
                          <Activity className="w-5 h-5 text-primary" />
                          <span className="font-medium">Performance Insights</span>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Your team's billable rate has increased by {stats?.timeStats.billable_rate || 0}% this month.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Reports;