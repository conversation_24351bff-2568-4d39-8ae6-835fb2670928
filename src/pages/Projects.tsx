import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { ProjectsListSimple } from '@/components/projects/ProjectsListSimple';
import { ProjectCreateSheet } from '@/components/projects/ProjectCreateSheet';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Search, Filter } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

const Projects = () => {
  const [isCreateSheetOpen, setIsCreateSheetOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  const { data: projects, isLoading, refetch } = useQuery({
    queryKey: ['projects', searchQuery, statusFilter],
    queryFn: async () => {
      try {
        let query = (supabase as any)
          .from('projects')
          .select('*')
          .order('created_at', { ascending: false });

        if (searchQuery) {
          query = query.ilike('name', `%${searchQuery}%`);
        }

        if (statusFilter !== 'all') {
          query = query.eq('status', statusFilter);
        }

        const { data, error } = await query;
        if (error) throw error;
        return data || [];
      } catch (error) {
        console.warn('Projects query failed:', error);
        return [];
      }
    },
  });

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Projects</h1>
            <p className="text-muted-foreground">
              Manage and track all your projects in one place
            </p>
          </div>
          <Button onClick={() => setIsCreateSheetOpen(true)} className="gap-2">
            <Plus className="w-4 h-4" />
            New Project
          </Button>
        </div>

        {/* Filters */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center">
          <div className="relative flex-1 md:max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search projects..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-muted-foreground" />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Projects</SelectItem>
                <SelectItem value="planning">Planning</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="on_hold">On Hold</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Projects List */}
        <ProjectsListSimple 
          projects={projects || []} 
          isLoading={isLoading} 
          onRefresh={refetch}
          onCreateProject={() => setIsCreateSheetOpen(true)}
        />

        {/* Create Project Dialog */}
        <ProjectCreateSheet
          open={isCreateSheetOpen}
          onOpenChange={setIsCreateSheetOpen}
          onProjectCreated={() => {
            refetch();
            setIsCreateSheetOpen(false);
          }}
        />
      </div>
    </DashboardLayout>
  );
};

export default Projects;