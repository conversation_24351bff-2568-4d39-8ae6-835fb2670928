import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { TeamDetailView } from '@/components/teams/TeamDetailView';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

const TeamDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  if (!id) {
    return (
      <DashboardLayout>
        <div className="text-center py-8">
          <h1 className="text-2xl font-bold text-destructive">Team not found</h1>
          <p className="text-muted-foreground">The team you're looking for doesn't exist.</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => navigate('/teams')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Teams
          </Button>
        </div>
        <TeamDetailView teamId={id} />
      </div>
    </DashboardLayout>
  );
};

export default TeamDetails;