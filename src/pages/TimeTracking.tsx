import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { TimeTracker } from '@/components/time/TimeTracker';
import { TimeEntries } from '@/components/time/TimeEntries';
import { ManualTimeEntry } from '@/components/time/ManualTimeEntry';
import { 
  Clock, 
  Timer,
  DollarSign,
  TrendingUp,
  Download,
  BarChart3
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthContext';
import { format, startOfWeek, endOfWeek } from 'date-fns';

const TimeTracking = () => {
  const [isManualEntryOpen, setIsManualEntryOpen] = useState(false);
  const { user } = useAuth();

  // Fetch weekly stats
  const { data: weeklyStats } = useQuery({
    queryKey: ['weekly-stats', user?.id],
    queryFn: async () => {
      const weekStart = startOfWeek(new Date());
      const weekEnd = endOfWeek(new Date());

      const { data: entries, error } = await (supabase as any)
        .from('time_entries')
        .select('duration_minutes, is_billable, hourly_rate, project_id')
        .eq('user_id', user!.id)
        .gte('start_time', weekStart.toISOString())
        .lte('start_time', weekEnd.toISOString())
        .not('end_time', 'is', null);

      if (error) throw error;

      const totalMinutes = entries.reduce((sum, entry) => sum + (entry.duration_minutes || 0), 0);
      const billableMinutes = entries
        .filter(entry => entry.is_billable)
        .reduce((sum, entry) => sum + (entry.duration_minutes || 0), 0);
      
      const totalEarnings = entries
        .filter(entry => entry.is_billable && entry.hourly_rate)
        .reduce((sum, entry) => {
          const hours = (entry.duration_minutes || 0) / 60;
          return sum + (hours * (entry.hourly_rate || 0));
        }, 0);

      const uniqueProjects = new Set(entries.map(entry => entry.project_id)).size;

      const formatDuration = (minutes: number) => {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return `${hours}h ${mins}m`;
      };

      return {
        totalHours: formatDuration(totalMinutes),
        billableHours: formatDuration(billableMinutes),
        earnings: `$${totalEarnings.toFixed(2)}`,
        projects: uniqueProjects,
        billableRate: totalMinutes > 0 ? Math.round((billableMinutes / totalMinutes) * 100) : 0,
      };
    },
    enabled: !!user,
  });

  return (
    <DashboardLayout>
      <div className="container mx-auto p-6 space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Time Tracking</h1>
            <p className="text-muted-foreground">
              Track time across projects and manage productivity with advanced billing features
            </p>
          </div>
          <Button 
            onClick={() => setIsManualEntryOpen(true)}
            className="gap-2"
          >
            <Clock className="w-4 h-4" />
            Manual Entry
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Hours</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{weeklyStats?.totalHours || '0h 0m'}</div>
              <p className="text-xs text-muted-foreground">This week</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Billable Hours</CardTitle>
              <Timer className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{weeklyStats?.billableHours || '0h 0m'}</div>
              <p className="text-xs text-muted-foreground">{weeklyStats?.billableRate || 0}% billable rate</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Earnings</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{weeklyStats?.earnings || '$0.00'}</div>
              <p className="text-xs text-muted-foreground">This week's earnings</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{weeklyStats?.projects || 0}</div>
              <p className="text-xs text-muted-foreground">Active projects</p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="tracker" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="tracker">Time Tracker</TabsTrigger>
            <TabsTrigger value="entries">Time Entries</TabsTrigger>
            <TabsTrigger value="reports">Reports & Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="tracker">
            <TimeTracker />
          </TabsContent>

          <TabsContent value="entries">
            <TimeEntries />
          </TabsContent>

          <TabsContent value="reports">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="w-5 h-5" />
                    Weekly Summary
                  </CardTitle>
                  <CardDescription>
                    Time breakdown by day this week
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span>Monday</span>
                      <span className="font-medium">8h 30m</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tuesday</span>
                      <span className="font-medium">7h 15m</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Wednesday</span>
                      <span className="font-medium">6h 45m</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Thursday</span>
                      <span className="font-medium">8h 00m</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Friday</span>
                      <span className="font-medium">2h 15m</span>
                    </div>
                    <hr />
                    <div className="flex justify-between font-bold">
                      <span>Total</span>
                      <span>{weeklyStats?.totalHours || '0h 0m'}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Billing Overview</CardTitle>
                  <CardDescription>
                    Earnings and billable time summary
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Total Billable</span>
                      <div className="text-right">
                        <div className="font-medium">{weeklyStats?.billableHours || '0h 0m'}</div>
                        <div className="text-sm text-muted-foreground">{weeklyStats?.billableRate || 0}%</div>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Weekly Earnings</span>
                      <div className="text-right">
                        <div className="font-medium text-lg text-success">{weeklyStats?.earnings || '$0.00'}</div>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Active Projects</span>
                      <div className="text-right">
                        <div className="font-medium">{weeklyStats?.projects || 0}</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        <ManualTimeEntry
          isOpen={isManualEntryOpen}
          onClose={() => setIsManualEntryOpen(false)}
        />
      </div>
    </DashboardLayout>
  );
};

export default TimeTracking;