import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TicketCreateSheet } from '@/components/support/TicketCreateSheet';
import { TicketDetailView } from '@/components/support/TicketDetailView';
import { 
  HeadphonesIcon, 
  Plus,
  Search,
  Clock,
  AlertCircle,
  CheckCircle,
  MessageSquare,
  User,
  Calendar,
  ArrowUpDown,
  ArrowLeft
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthContext';
import { format, parseISO } from 'date-fns';

interface SupportTicket {
  id: string;
  ticket_id: string;
  title: string;
  description: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high';
  created_by: string;
  assigned_to: string | null;
  project_id: string | null;
  created_at: string;
  updated_at: string;
  projects?: {
    name: string;
    project_id: string;
  };
  creator: {
    first_name: string;
    last_name: string;
    email: string;
  };
  assignee?: {
    first_name: string;
    last_name: string;
  } | null;
  _count?: {
    responses: number;
  };
}

const SupportTickets = () => {
  const navigate = useNavigate();
  const [isCreateTicketOpen, setIsCreateTicketOpen] = useState(false);
  const [selectedTicketId, setSelectedTicketId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [assigneeFilter, setAssigneeFilter] = useState('all');
  const [activeTab, setActiveTab] = useState('all');

  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Fetch tickets with real data
  const { data: tickets = [], isLoading } = useQuery({
    queryKey: ['support-tickets', activeTab, searchQuery, statusFilter, priorityFilter, assigneeFilter],
    queryFn: async () => {
      let query = (supabase as any)
        .from('support_tickets')
        .select(`
          *,
          projects(name, project_id)
        `)
        .order('created_at', { ascending: false });

      // Apply filters based on user role and tab
      if (activeTab === 'my') {
        // Only filter for non-admin users
        const { data: userRoles } = await (supabase as any)
          .from('user_roles')
          .select('role')
          .eq('user_id', user?.id);
        
        const isAdmin = userRoles?.some((role: any) => role.role === 'admin');
        
        if (!isAdmin) {
          query = query.or(`created_by.eq.${user?.id},assigned_to.eq.${user?.id}`);
        }
      } else if (activeTab === 'unassigned') {
        query = query.is('assigned_to', null);
      }

      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter);
      }

      if (priorityFilter !== 'all') {
        query = query.eq('priority', priorityFilter);
      }

      if (assigneeFilter !== 'all') {
        if (assigneeFilter === 'unassigned') {
          query = query.is('assigned_to', null);
        } else {
          query = query.eq('assigned_to', assigneeFilter);
        }
      }

      if (searchQuery) {
        query = query.or(`title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%,ticket_id.ilike.%${searchQuery}%`);
      }

      const { data, error } = await query;
      if (error) {
        console.error('Support tickets query error:', error);
        throw error;
      }

      // Fetch creator and assignee profiles separately
      const ticketsWithProfiles = await Promise.all(
        (data || []).map(async (ticket: any) => {
          const [creatorData, assigneeData] = await Promise.all([
            ticket.created_by ? supabase
              .from('profiles')
              .select('first_name, last_name, email')
              .eq('user_id', ticket.created_by)
              .maybeSingle() : Promise.resolve({ data: null }),
            ticket.assigned_to ? supabase
              .from('profiles')
              .select('first_name, last_name')
              .eq('user_id', ticket.assigned_to)
              .maybeSingle() : Promise.resolve({ data: null })
          ]);

          return {
            ...ticket,
            creator: creatorData.data,
            assignee: assigneeData.data
          };
        })
      );

      return ticketsWithProfiles as SupportTicket[];
    },
    enabled: !!user,
  });

  // Fetch ticket statistics
  const { data: stats } = useQuery({
    queryKey: ['ticket-stats'],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('support_tickets')
        .select('status');
      
      if (error) throw error;

      const open = data.filter((t: any) => t.status === 'open').length;
      const in_progress = data.filter((t: any) => t.status === 'in_progress').length;
      const resolved = data.filter((t: any) => t.status === 'resolved').length;
      const closed = data.filter((t: any) => t.status === 'closed').length;

      return {
        open,
        in_progress,
        resolved: resolved + closed,
        avg_response_time: '2.5h' // This would be calculated from actual response times
      };
    },
    enabled: !!user,
  });

  // Fetch team members for filter
  const { data: teamMembers = [] } = useQuery({
    queryKey: ['team-members'],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('profiles')
        .select('user_id, first_name, last_name')
        .order('first_name');
      
      if (error) throw error;
      return data;
    },
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'destructive';
      case 'in_progress': return 'default';
      case 'resolved': return 'secondary';
      default: return 'outline';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'outline';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return <AlertCircle className="w-4 h-4" />;
      case 'medium': return <Clock className="w-4 h-4" />;
      case 'low': return <CheckCircle className="w-4 h-4" />;
      default: return null;
    }
  };

  // If viewing ticket detail, show detail view
  if (selectedTicketId) {
    return (
      <DashboardLayout>
        <div className="container mx-auto p-6">
          <TicketDetailView
            ticketId={selectedTicketId}
            onBack={() => setSelectedTicketId(null)}
          />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto p-6 space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-4 mb-2">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => navigate('/')}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Dashboard
              </Button>
            </div>
            <h1 className="text-3xl font-bold text-foreground">Support Tickets</h1>
            <p className="text-muted-foreground">
              Manage support requests with file attachments and team collaboration
            </p>
          </div>
          <Button onClick={() => setIsCreateTicketOpen(true)} className="gap-2">
            <Plus className="w-4 h-4" />
            New Ticket
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Open Tickets</CardTitle>
              <AlertCircle className="h-4 w-4 text-destructive" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.open || 0}</div>
              <p className="text-xs text-muted-foreground">Needs attention</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">In Progress</CardTitle>
              <Clock className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.in_progress || 0}</div>
              <p className="text-xs text-muted-foreground">Being worked on</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Resolved</CardTitle>
              <CheckCircle className="h-4 w-4 text-success" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.resolved || 0}</div>
              <p className="text-xs text-muted-foreground">This month</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Response</CardTitle>
              <HeadphonesIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.avg_response_time || 'N/A'}</div>
              <p className="text-xs text-muted-foreground">Average response time</p>
            </CardContent>
          </Card>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <div className="flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="all">All Tickets</TabsTrigger>
              <TabsTrigger value="my">My Tickets</TabsTrigger>
              <TabsTrigger value="unassigned">Unassigned</TabsTrigger>
            </TabsList>

            {/* Filters */}
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search tickets..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                </SelectContent>
              </Select>
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priority</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
              <Select value={assigneeFilter} onValueChange={setAssigneeFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Assignee" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Assignees</SelectItem>
                  <SelectItem value="unassigned">Unassigned</SelectItem>
                  {teamMembers.map((member) => (
                    <SelectItem key={member.user_id} value={member.user_id}>
                      {member.first_name} {member.last_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <TabsContent value={activeTab} className="space-y-4">
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="text-muted-foreground mt-2">Loading tickets...</p>
              </div>
            ) : tickets.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <HeadphonesIcon className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No tickets found</h3>
                  <p className="text-muted-foreground">
                    {activeTab === 'my' 
                      ? 'No tickets assigned to you or created by you.' 
                      : activeTab === 'unassigned'
                      ? 'All tickets are currently assigned.'
                      : 'No tickets match your current filters.'}
                  </p>
                </CardContent>
              </Card>
            ) : (
              tickets.map((ticket) => (
                <Card 
                  key={ticket.id} 
                  className="hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => setSelectedTicketId(ticket.id)}
                >
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <CardTitle className="text-lg hover:text-primary transition-colors">
                            {ticket.title}
                          </CardTitle>
                          <Badge variant="outline">{ticket.ticket_id}</Badge>
                        </div>
                        <CardDescription className="line-clamp-2">
                          {ticket.description}
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={getStatusColor(ticket.status)}>
                          {ticket.status.replace('_', ' ')}
                        </Badge>
                        <Badge variant={getPriorityColor(ticket.priority)} className="gap-1">
                          {getPriorityIcon(ticket.priority)}
                          {ticket.priority}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <User className="w-4 h-4" />
                          Created by {ticket.creator.first_name} {ticket.creator.last_name}
                        </div>
                        {ticket.assignee && (
                          <div className="flex items-center gap-1">
                            <User className="w-4 h-4" />
                            Assigned to {ticket.assignee.first_name} {ticket.assignee.last_name}
                          </div>
                        )}
                        <div className="flex items-center gap-1">
                          <MessageSquare className="w-4 h-4" />
                          {ticket._count?.responses || 0} responses
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        {format(parseISO(ticket.created_at), 'MMM d, yyyy')}
                      </div>
                    </div>
                    {ticket.projects && (
                      <div className="mt-2">
                        <Badge variant="outline" className="text-xs">
                          Project: {ticket.projects.name}
                        </Badge>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))
            )}
          </TabsContent>
        </Tabs>

        <TicketCreateSheet
          isOpen={isCreateTicketOpen}
          onClose={() => setIsCreateTicketOpen(false)}
          onTicketCreated={() => {
            queryClient.invalidateQueries({ queryKey: ['support-tickets'] });
            queryClient.invalidateQueries({ queryKey: ['ticket-stats'] });
          }}
        />
      </div>
    </DashboardLayout>
  );
};

export default SupportTickets;