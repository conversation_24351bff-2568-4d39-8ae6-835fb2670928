import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthContext';

export const useUserRole = () => {
  const { user } = useAuth();
  const [userRoles, setUserRoles] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUserRoles = async () => {
      if (!user) {
        setUserRoles([]);
        setLoading(false);
        return;
      }

      try {
        const { data, error } = await (supabase as any)
          .from('user_roles')
          .select('role')
          .eq('user_id', user.id);

        if (error) {
          console.error('Error fetching user roles:', error);
          setUserRoles([]);
        } else {
          const roles = data?.map((item: any) => item.role) || [];
          setUserRoles(roles);
        }
      } catch (error) {
        console.error('Error in fetchUserRoles:', error);
        setUserRoles([]);
      } finally {
        setLoading(false);
      }
    };

    fetchUserRoles();
  }, [user]);

  const isAdmin = userRoles.includes('admin');
  const isProjectManager = userRoles.includes('project_manager');
  const isUser = userRoles.includes('user');

  return {
    userRoles,
    loading,
    isAdmin,
    isProjectManager,
    isUser,
    hasRole: (role: string) => userRoles.includes(role),
  };
};