# RatioHub AWS S3 Deployment Guide

## Overview
RatioHub is deployed to AWS S3 with CloudFront CDN for global distribution.

## AWS Configuration
- **S3 Bucket**: `ratiohub` (ap-south-1 region)
- **CloudFront Distribution**: `E1UG94K7NZNW4Z`
- **CloudFront Domain**: `d3vvwsd9nr6exp.cloudfront.net`

## Deployment

### Prerequisites
- AWS CLI installed and configured with credentials
- Node.js and npm installed

### Deploy Application
```bash
# Build and deploy to S3 + CloudFront
npm run deploy
```

This will:
- Build the React application with Vite
- Upload files to S3 with optimized caching
- Invalidate CloudFront cache automatically
- Configure static website hosting

## URLs
- **S3 Website**: http://ratiohub.s3-website.ap-south-1.amazonaws.com
- **CloudFront**: https://d3vvwsd9nr6exp.cloudfront.net
- **S3 Console**: https://s3.console.aws.amazon.com/s3/buckets/ratiohub

## Environment Variables
Production environment is configured in `.env.production`:
```env
VITE_SUPABASE_URL=https://rgcxrksmdkzarcdlscpk.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
VITE_APP_ENV=production
```

## Caching Strategy
- **Assets** (`/assets/*`): 1 year cache
- **Images**: 1 day cache
- **HTML**: No cache
- **Other files**: 1 hour cache
