#!/bin/bash

# RatioHub S3 Deployment Script
# This script builds the React/Vite app and deploys it to AWS S3

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BUCKET_NAME="ratiohub"
REGION="ap-south-1"  # Asia Pacific (Mumbai) region
CLOUDFRONT_DISTRIBUTION_ID="E1UG94K7NZNW4Z"  # CloudFront distribution ID
CLOUDFRONT_DOMAIN="d3vvwsd9nr6exp.cloudfront.net"  # CloudFront domain name

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if AWS CLI is installed
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install it first:"
        echo "  brew install awscli  # On macOS"
        echo "  pip install awscli   # Using pip"
        exit 1
    fi
    print_success "AWS CLI is installed"
}

# Check if AWS credentials are configured
check_aws_credentials() {
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS credentials are not configured. Please run:"
        echo "  ./setup-aws.sh"
        echo "Or set environment variables:"
        echo "  export AWS_ACCESS_KEY_ID=your_access_key"
        echo "  export AWS_SECRET_ACCESS_KEY=your_secret_key"
        exit 1
    fi
    print_success "AWS credentials are configured"
}

# Check if bucket exists
check_bucket() {
    if aws s3 ls "s3://$BUCKET_NAME" &> /dev/null; then
        print_success "S3 bucket '$BUCKET_NAME' exists"
    else
        print_error "S3 bucket '$BUCKET_NAME' does not exist or you don't have access"
        echo "Please run ./setup-aws.sh to create the bucket"
        exit 1
    fi
}

# Check if Node.js and npm are installed
check_node() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    print_success "Node.js and npm are installed"
}

# Build the React/Vite application
build_app() {
    print_status "Building React/Vite application..."
    
    if [ ! -f "package.json" ]; then
        print_error "package.json not found. Are you in the correct directory?"
        exit 1
    fi
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        print_status "Installing dependencies..."
        npm install
    fi
    
    # Build the React application
    print_status "Building React application..."

    # Try multiple approaches to build
    if npm run build; then
        print_success "Build completed with npm run build"
    elif npx vite build; then
        print_success "Build completed with npx vite"
    elif ./node_modules/.bin/vite build; then
        print_success "Build completed with local vite"
    else
        print_error "All build methods failed. Please check your Vite configuration."
        exit 1
    fi

    if [ ! -d "dist" ]; then
        print_error "Build failed - 'dist' directory not found"
        exit 1
    fi
    
    print_success "Application built successfully"
}

# Deploy to S3
deploy_to_s3() {
    print_status "Deploying to S3 bucket: $BUCKET_NAME"
    
    # Sync static assets with long cache (JS, CSS, images with hashes)
    print_status "Uploading static assets with long cache..."
    aws s3 sync dist/assets/ s3://$BUCKET_NAME/assets/ \
        --delete \
        --cache-control "public, max-age=31536000, immutable" \
        --metadata-directive REPLACE
    
    # Upload images with medium cache
    if [ -d "dist/images" ]; then
        print_status "Uploading images with medium cache..."
        aws s3 sync dist/images/ s3://$BUCKET_NAME/images/ \
            --delete \
            --cache-control "public, max-age=86400" \
            --metadata-directive REPLACE
    fi
    
    # Upload HTML and other files with short cache
    print_status "Uploading HTML pages and other files..."
    aws s3 sync dist/ s3://$BUCKET_NAME/ \
        --delete \
        --exclude "assets/*" \
        --exclude "images/*" \
        --cache-control "public, max-age=3600" \
        --metadata-directive REPLACE
    
    # Set specific cache control for index.html (no cache)
    print_status "Setting no-cache for index.html..."
    aws s3 cp dist/index.html s3://$BUCKET_NAME/index.html \
        --cache-control "no-cache, no-store, must-revalidate" \
        --metadata-directive REPLACE
    
    print_success "Files uploaded to S3"
}

# Configure S3 bucket for static website hosting
configure_website() {
    print_status "Configuring S3 bucket for static website hosting..."
    
    # Create website configuration
    cat > website-config.json << EOF
{
    "IndexDocument": {
        "Suffix": "index.html"
    },
    "ErrorDocument": {
        "Key": "index.html"
    }
}
EOF
    
    # Apply website configuration
    aws s3api put-bucket-website \
        --bucket $BUCKET_NAME \
        --website-configuration file://website-config.json
    
    # Clean up temp file
    rm website-config.json
    
    print_success "Website configuration applied"
}

# Set bucket policy for public read access
set_bucket_policy() {
    print_status "Setting bucket policy for public read access..."
    
    # Create bucket policy
    cat > bucket-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::$BUCKET_NAME/*"
        }
    ]
}
EOF
    
    # Apply bucket policy
    aws s3api put-bucket-policy \
        --bucket $BUCKET_NAME \
        --policy file://bucket-policy.json
    
    # Clean up temp file
    rm bucket-policy.json
    
    print_success "Bucket policy applied"
}

# Invalidate CloudFront cache
invalidate_cloudfront() {
    print_status "Invalidating CloudFront cache..."
    aws cloudfront create-invalidation \
        --distribution-id $CLOUDFRONT_DISTRIBUTION_ID \
        --paths "/*"
    print_success "CloudFront invalidation created"
}

# Get website URL
get_website_url() {
    WEBSITE_URL="http://$BUCKET_NAME.s3-website.$REGION.amazonaws.com"
    print_success "Deployment complete!"
    echo ""
    echo "🌐 Website URL: $WEBSITE_URL"
    echo "🌐 S3 Origin URL: https://$BUCKET_NAME.s3.$REGION.amazonaws.com"
    echo "🚀 CloudFront URL: https://$CLOUDFRONT_DOMAIN"
    echo "📊 S3 Console: https://s3.console.aws.amazon.com/s3/buckets/$BUCKET_NAME"
    echo ""
    echo "Note: It may take a few minutes for changes to propagate."
    echo "CloudFront cache invalidation may take 10-15 minutes to complete."
}

# Main deployment function
main() {
    echo "🚀 RatioHub S3 Deployment Script"
    echo "================================="
    echo ""
    
    # Run all checks and deployment steps
    check_aws_cli
    check_aws_credentials
    check_node
    check_bucket
    build_app
    deploy_to_s3
    configure_website
    set_bucket_policy
    invalidate_cloudfront
    get_website_url
}

# Run main function
main "$@"
