-- Create comprehensive SAP implementation data
DO $$
DECLARE
    admin_user_id uuid := '183d5d61-4b48-4f60-b4a3-1cc45d76e713';
    
    sap_team_id uuid := gen_random_uuid();
    technical_team_id uuid := gen_random_uuid();
    
    project1_id uuid := gen_random_uuid();
    project2_id uuid := gen_random_uuid();
    project3_id uuid := gen_random_uuid();
    
    task1_id uuid := gen_random_uuid();
    task2_id uuid := gen_random_uuid();
    task3_id uuid := gen_random_uuid();
    task4_id uuid := gen_random_uuid();
    task5_id uuid := gen_random_uuid();
    task6_id uuid := gen_random_uuid();
    task7_id uuid := gen_random_uuid();
    task8_id uuid := gen_random_uuid();
    task9_id uuid := gen_random_uuid();
    task10_id uuid := gen_random_uuid();
    
    ticket1_id uuid := gen_random_uuid();
    ticket2_id uuid := gen_random_uuid();
    ticket3_id uuid := gen_random_uuid();
    ticket4_id uuid := gen_random_uuid();
    ticket5_id uuid := gen_random_uuid();
BEGIN
    -- Create SAP implementation teams
    INSERT INTO public.teams (id, name, description, created_by, is_active) VALUES
    (sap_team_id, 'SAP Functional Team', 'Core SAP functional consultants handling business process implementation', admin_user_id, true),
    (technical_team_id, 'SAP Technical Team', 'Technical specialists for SAP development, integration, and security', admin_user_id, true);

    -- Add admin user to teams
    INSERT INTO public.team_members (team_id, user_id, role) VALUES
    (sap_team_id, admin_user_id, 'lead'),
    (technical_team_id, admin_user_id, 'lead');

    -- Create SAP implementation projects
    INSERT INTO public.projects (id, project_id, name, description, status, created_by, team_id, start_date, end_date, budget, hourly_rate, currency, is_billable) VALUES
    (project1_id, 'SA001', 'Global Manufacturing SAP S/4HANA Implementation', 'End-to-end SAP S/4HANA implementation for global manufacturing company including FI, CO, MM, PP, SD modules with integration to existing systems', 'active', admin_user_id, sap_team_id, '2024-01-15', '2025-06-30', 2500000.00, 250.00, 'USD', true),
    (project2_id, 'SA002', 'Retail Chain SAP Commerce Cloud Integration', 'SAP Commerce Cloud implementation with integration to SAP ERP for omnichannel retail operations', 'planning', admin_user_id, technical_team_id, '2024-03-01', '2024-12-15', 1200000.00, 275.00, 'USD', true),
    (project3_id, 'SA003', 'Financial Services SAP SuccessFactors HCM', 'SAP SuccessFactors Employee Central and Performance Management implementation for financial services company', 'active', admin_user_id, sap_team_id, '2024-02-01', '2024-11-30', 850000.00, 225.00, 'USD', true);

    -- Create SAP tasks for projects
    INSERT INTO public.tasks (id, task_id, project_id, title, description, status, priority, assigned_to, created_by, due_date, estimated_hours, is_billable) VALUES
    (task1_id, 'SA001-T001', project1_id, 'SAP FI Module Configuration', 'Configure Financial Accounting module including chart of accounts, fiscal year, and company codes', 'in_progress', 'high', admin_user_id, admin_user_id, '2024-08-15', 80, true),
    (task2_id, 'SA001-T002', project1_id, 'SAP CO Module Setup', 'Configure Controlling module with cost centers, profit centers, and internal orders', 'todo', 'high', admin_user_id, admin_user_id, '2024-08-30', 60, true),
    (task3_id, 'SA001-T003', project1_id, 'SAP MM Procurement Configuration', 'Set up Materials Management including purchasing, vendor management, and inventory processes', 'todo', 'medium', admin_user_id, admin_user_id, '2024-09-15', 120, true),
    (task4_id, 'SA001-T004', project1_id, 'SAP SD Sales Configuration', 'Configure Sales and Distribution module for order processing and billing', 'todo', 'medium', admin_user_id, admin_user_id, '2024-09-30', 90, true),
    (task5_id, 'SA001-T005', project1_id, 'ABAP Custom Development', 'Develop custom reports and enhancements for specific business requirements', 'todo', 'medium', admin_user_id, admin_user_id, '2024-10-15', 160, true),
    
    (task6_id, 'SA002-T001', project2_id, 'Commerce Cloud Environment Setup', 'Provision and configure SAP Commerce Cloud development environment', 'completed', 'high', admin_user_id, admin_user_id, '2024-04-01', 40, true),
    (task7_id, 'SA002-T002', project2_id, 'Product Catalog Integration', 'Integrate product catalog between Commerce Cloud and SAP ERP', 'in_progress', 'high', admin_user_id, admin_user_id, '2024-08-30', 80, true),
    
    (task8_id, 'SA003-T001', project3_id, 'SuccessFactors Employee Central Setup', 'Configure organizational structure and employee data model', 'completed', 'high', admin_user_id, admin_user_id, '2024-03-30', 60, true),
    (task9_id, 'SA003-T002', project3_id, 'Performance Management Configuration', 'Set up performance review cycles and goal management', 'in_progress', 'medium', admin_user_id, admin_user_id, '2024-09-15', 70, true),
    (task10_id, 'SA003-T003', project3_id, 'Employee Data Migration', 'Migrate existing employee data from legacy HR system', 'todo', 'high', admin_user_id, admin_user_id, '2024-10-30', 100, true);

    -- Create time entries for SAP projects
    INSERT INTO public.time_entries (project_id, task_id, user_id, start_time, end_time, duration_minutes, description, hourly_rate, is_billable) VALUES
    (project1_id, task1_id, admin_user_id, '2024-08-01 09:00:00+00', '2024-08-01 17:00:00+00', 480, 'FI module configuration - chart of accounts setup', 250.00, true),
    (project1_id, task1_id, admin_user_id, '2024-08-02 09:00:00+00', '2024-08-02 17:00:00+00', 480, 'FI module configuration - fiscal year setup', 250.00, true),
    (project1_id, task1_id, admin_user_id, '2024-08-05 09:00:00+00', '2024-08-05 17:00:00+00', 480, 'FI module configuration - company code configuration', 250.00, true),
    
    (project2_id, task6_id, admin_user_id, '2024-03-15 09:00:00+00', '2024-03-15 17:00:00+00', 480, 'Commerce Cloud environment provisioning', 275.00, true),
    (project2_id, task7_id, admin_user_id, '2024-08-10 09:00:00+00', '2024-08-10 13:00:00+00', 240, 'Product catalog integration analysis', 275.00, true),
    
    (project3_id, task8_id, admin_user_id, '2024-03-01 09:00:00+00', '2024-03-01 17:00:00+00', 480, 'SuccessFactors organizational structure setup', 225.00, true),
    (project3_id, task9_id, admin_user_id, '2024-08-12 09:00:00+00', '2024-08-12 17:00:00+00', 480, 'Performance management cycle configuration', 225.00, true);

    -- Create support tickets for SAP projects
    INSERT INTO public.support_tickets (id, ticket_id, title, description, priority, status, created_by, project_id) VALUES
    (ticket1_id, 'TKT-001', 'SAP S/4HANA Performance Issues in DEV Environment', 'Development system experiencing slow response times during FI posting transactions', 'high', 'in_progress', admin_user_id, project1_id),
    (ticket2_id, 'TKT-002', 'SAP Transport Request Failed', 'Transport request TR001 failed to import into QAS system with syntax errors', 'medium', 'open', admin_user_id, project1_id),
    (ticket3_id, 'TKT-003', 'Commerce Cloud User Authentication Error', 'Users unable to login to Commerce Cloud storefront after recent deployment', 'high', 'open', admin_user_id, project2_id),
    (ticket4_id, 'TKT-004', 'SuccessFactors Integration Timeout', 'Employee data sync from payroll system timing out after 5 minutes', 'medium', 'in_progress', admin_user_id, project3_id),
    (ticket5_id, 'TKT-005', 'SAP ABAP Memory Dump in Production', 'ABAP runtime error STORAGE_PARAMETERS_WRONG_SET occurring in production FI posting', 'high', 'open', admin_user_id, project1_id);

    -- Create task comments
    INSERT INTO public.task_comments (task_id, user_id, content) VALUES
    (task1_id, admin_user_id, 'FI module configuration is progressing well. Chart of accounts structure has been finalized with the client finance team.'),
    (task1_id, admin_user_id, 'Need to coordinate with the client on fiscal year variant configuration before proceeding.'),
    (task7_id, admin_user_id, 'Product catalog integration requires additional API endpoints from the ERP team. Waiting for technical specification.'),
    (task9_id, admin_user_id, 'Performance review template approved by HR. Starting configuration of review cycles.'),
    (task10_id, admin_user_id, 'Employee data quality assessment complete. 95% of records are ready for migration.');

    -- Create file attachments
    INSERT INTO public.file_attachments (project_id, task_id, file_name, file_url, file_type, file_size, uploaded_by) VALUES
    (project1_id, task1_id, 'SAP_FI_Configuration_Guide.pdf', '/files/sap-fi-config.pdf', 'application/pdf', 2048576, admin_user_id),
    (project1_id, task3_id, 'MM_Purchasing_Process_Design.docx', '/files/mm-purchasing-design.docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 1024000, admin_user_id),
    (project2_id, task7_id, 'Commerce_Cloud_Integration_Specs.xlsx', '/files/commerce-integration-specs.xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 512000, admin_user_id),
    (project3_id, task8_id, 'SuccessFactors_Org_Chart.png', '/files/sf-org-chart.png', 'image/png', 256000, admin_user_id),
    (project1_id, null, 'SAP_S4HANA_Project_Charter.pdf', '/files/s4hana-charter.pdf', 'application/pdf', 3072000, admin_user_id);

    -- Create project activity logs
    INSERT INTO public.project_activity_logs (project_id, user_id, activity_type, entity_type, entity_id, description, metadata) VALUES
    (project1_id, admin_user_id, 'project_created', 'project', project1_id, 'Created SAP S/4HANA implementation project', '{"budget": 2500000, "team": "SAP Functional Team"}'),
    (project1_id, admin_user_id, 'task_created', 'task', task1_id, 'Created FI module configuration task', '{"priority": "high", "estimated_hours": 80}'),
    (project1_id, admin_user_id, 'task_status_updated', 'task', task1_id, 'Started FI module configuration', '{"old_status": "todo", "new_status": "in_progress"}'),
    (project1_id, admin_user_id, 'file_uploaded', 'file', null, 'Uploaded SAP FI Configuration Guide', '{"file_name": "SAP_FI_Configuration_Guide.pdf", "file_size": 2048576}'),
    (project2_id, admin_user_id, 'project_created', 'project', project2_id, 'Created Commerce Cloud integration project', '{"budget": 1200000, "team": "SAP Technical Team"}'),
    (project3_id, admin_user_id, 'project_created', 'project', project3_id, 'Created SuccessFactors HCM implementation', '{"budget": 850000, "team": "SAP Functional Team"}'),
    (project3_id, admin_user_id, 'task_completed', 'task', task8_id, 'Completed SuccessFactors Employee Central setup', '{"completion_date": "2024-03-30"}');

END $$;