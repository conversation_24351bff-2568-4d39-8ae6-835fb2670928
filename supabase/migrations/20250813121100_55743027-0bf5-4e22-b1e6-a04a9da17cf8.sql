-- Sample data insertion with proper UUID format
INSERT INTO projects (id, name, project_id, description, status, created_by, start_date, end_date, budget, hourly_rate, is_billable, currency) VALUES
('********-1111-1111-1111-********1111'::uuid, 'SAP S/4HANA Finance Implementation', 'SA001', 'Complete SAP S/4HANA Finance module implementation for enterprise client', 'active', '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid, '2024-01-15', '2024-08-15', 500000, 150, true, 'USD'),
('*************-2222-2222-************'::uuid, 'SAP SuccessFactors HR Transformation', 'SA002', 'SAP SuccessFactors implementation for global HR processes', 'active', '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid, '2024-02-01', '2024-09-30', 350000, 140, true, 'USD'),
('*************-3333-3333-************'::uuid, 'SAP Ariba Procurement Platform', 'SA003', 'SAP Ariba implementation for procurement and sourcing optimization', 'planning', '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid, '2024-04-01', '2024-12-15', 275000, 135, true, 'USD');

-- Insert sample teams
INSERT INTO teams (id, name, description, created_by, is_active) VALUES
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'::uuid, 'SAP Finance Team', 'Specialized team for SAP Finance implementations', '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid, true),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb'::uuid, 'SAP HR Team', 'Expert team for SAP HR and SuccessFactors projects', '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid, true);

-- Update projects with team assignments  
UPDATE projects SET team_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'::uuid WHERE id = '********-1111-1111-1111-********1111'::uuid;
UPDATE projects SET team_id = 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb'::uuid WHERE id = '*************-2222-2222-************'::uuid;

-- Add user to teams
INSERT INTO team_members (team_id, user_id, role) VALUES
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'::uuid, '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid, 'lead'),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb'::uuid, '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid, 'lead');

-- Insert phases with correct status values: planning, in_progress, completed, on_hold
INSERT INTO phases (id, project_id, name, description, status, start_date, end_date, order_index, created_by) VALUES
('f1111111-1111-1111-1111-********1111'::uuid, '********-1111-1111-1111-********1111'::uuid, 'Discovery & Planning', 'Requirements gathering and project planning phase', 'completed', '2024-01-15', '2024-02-28', 1, '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid),
('f222**************-2222-************'::uuid, '********-1111-1111-1111-********1111'::uuid, 'Design & Configuration', 'System design and initial configuration', 'in_progress', '2024-03-01', '2024-05-15', 2, '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid),
('f333**************-3333-************'::uuid, '********-1111-1111-1111-********1111'::uuid, 'Development & Testing', 'Custom development and system testing', 'planning', '2024-05-16', '2024-07-15', 3, '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid);

-- Insert milestones
INSERT INTO milestones (id, project_id, phase_id, name, description, due_date, is_completed, completed_by, completed_at) VALUES
('********-**************-************'::uuid, '********-1111-1111-1111-********1111'::uuid, 'f1111111-1111-1111-1111-********1111'::uuid, 'Requirements Document Approved', 'Complete business requirements documentation', '2024-02-15', true, '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid, '2024-02-14'),
('*************-4444-5555-************'::uuid, '********-1111-1111-1111-********1111'::uuid, 'f222**************-2222-************'::uuid, 'System Design Complete', 'Technical design and architecture finalized', '2024-04-15', false, null, null),
('*************-5555-6666-************'::uuid, '********-1111-1111-1111-********1111'::uuid, 'f333**************-3333-************'::uuid, 'Development Phase Ready', 'Development environment setup complete', '2024-06-01', false, null, null);

-- Insert tasks with various statuses
INSERT INTO tasks (id, project_id, milestone_id, task_id, title, description, status, priority, assigned_to, created_by, due_date, estimated_hours, actual_hours, is_billable) VALUES
('aaaaaaaa-1111-**************44444444'::uuid, '********-1111-1111-1111-********1111'::uuid, '********-**************-************'::uuid, 'SA001-T001', 'Conduct Stakeholder Interviews', 'Interview key stakeholders to gather business requirements', 'completed', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid, '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid, '2024-02-10', 40, 38, true),
('bbbbbbbb-**************-************'::uuid, '********-1111-1111-1111-********1111'::uuid, '*************-4444-5555-************'::uuid, 'SA001-T002', 'Design Chart of Accounts Structure', 'Create and validate chart of accounts for S/4HANA', 'in_progress', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid, '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid, '2024-04-10', 24, 12, true),
('cccccccc-**************-************'::uuid, '********-1111-1111-1111-********1111'::uuid, '*************-4444-5555-************'::uuid, 'SA001-T003', 'Configure Financial Accounting Module', 'Set up basic FI configuration in SAP S/4HANA', 'todo', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid, '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid, '2024-04-20', 56, 0, true);

-- Insert support tickets
INSERT INTO support_tickets (id, ticket_id, title, description, status, priority, created_by, project_id) VALUES
('dddddddd-1111-**************44444444'::uuid, 'TKT-001', 'SAP System Performance Issue', 'Slow response time in FI module during peak hours', 'open', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid, '********-1111-1111-1111-********1111'::uuid),
('eeeeeeee-**************-************'::uuid, 'TKT-002', 'Chart of Accounts Validation', 'Need validation for proposed chart of accounts structure', 'in_progress', 'medium', '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid, '********-1111-1111-1111-********1111'::uuid);