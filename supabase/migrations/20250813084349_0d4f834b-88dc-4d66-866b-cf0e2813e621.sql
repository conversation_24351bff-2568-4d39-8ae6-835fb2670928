-- Fix function search path security issues
CREATE OR <PERSON><PERSON>LACE FUNCTION public.generate_invitation_token()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    RETURN encode(gen_random_bytes(32), 'hex');
END;
$$;

CREATE OR REPLACE FUNCTION public.accept_invitation(invitation_token TEXT, user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    invitation_record RECORD;
    team_id UUID;
BEGIN
    -- Find valid invitation
    SELECT * INTO invitation_record
    FROM public.user_invitations
    WHERE invitation_token = accept_invitation.invitation_token
    AND status = 'pending'
    AND expires_at > now();
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Update invitation status
    UPDATE public.user_invitations
    SET status = 'accepted',
        accepted_at = now(),
        updated_at = now()
    WHERE invitation_token = accept_invitation.invitation_token;
    
    -- Assign role to user
    INSERT INTO public.user_roles (user_id, role, assigned_by)
    VALUES (user_id, invitation_record.role, invitation_record.invited_by)
    ON CONFLICT (user_id, role) DO NOTHING;
    
    -- Add user to teams if specified
    IF invitation_record.team_ids IS NOT NULL THEN
        FOREACH team_id IN ARRAY invitation_record.team_ids
        LOOP
            INSERT INTO public.team_members (team_id, user_id, role)
            VALUES (team_id, user_id, 'member')
            ON CONFLICT (team_id, user_id) DO NOTHING;
        END LOOP;
    END IF;
    
    RETURN TRUE;
END;
$$;