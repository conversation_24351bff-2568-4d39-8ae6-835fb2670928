-- First, get the admin user ID for preservation
DO $$
DECLARE
    admin_user_id uuid;
BEGIN
    -- Get admin user ID
    SELECT user_id INTO admin_user_id FROM profiles WHERE email = '<EMAIL>';
    
    -- Clear all existing data except admin user
    DELETE FROM project_activity_logs;
    DELETE FROM task_comments;
    DELETE FROM file_attachments;
    DELETE FROM time_entries;
    DELETE FROM tasks;
    DELETE FROM milestones;
    DELETE FROM phases;
    DELETE FROM support_tickets;
    DELETE FROM team_members WHERE user_id != admin_user_id;
    DELETE FROM teams WHERE created_by != admin_user_id;
    DELETE FROM projects WHERE created_by != admin_user_id;
    DELETE FROM user_roles WHERE user_id != admin_user_id;
    DELETE FROM profiles WHERE user_id != admin_user_id;
    
    -- Update admin user profile
    UPDATE profiles 
    SET first_name = 'Surendra', 
        last_name = 'Ganne',
        email = '<EMAIL>',
        position = 'SAP Practice Director',
        department = 'Digital Transformation'
    WHERE email = '<EMAIL>';
END $$;