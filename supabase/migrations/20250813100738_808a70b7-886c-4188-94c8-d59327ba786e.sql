-- Create SAP implementation projects with correct status values
DO $$
DECLARE
    admin_user_id uuid := '183d5d61-4b48-4f60-b4a3-1cc45d76e713';
    sap_team_id uuid := gen_random_uuid();
    technical_team_id uuid := gen_random_uuid();
    project1_id uuid := gen_random_uuid();
    project2_id uuid := gen_random_uuid();
    project3_id uuid := gen_random_uuid();
    phase1_id uuid := gen_random_uuid();
    phase2_id uuid := gen_random_uuid();
    phase3_id uuid := gen_random_uuid();
    milestone1_id uuid := gen_random_uuid();
    milestone2_id uuid := gen_random_uuid();
    task1_id uuid := gen_random_uuid();
    task2_id uuid := gen_random_uuid();
    task3_id uuid := gen_random_uuid();
BEGIN
    -- Create SAP implementation teams
    INSERT INTO public.teams (id, name, description, created_by, is_active) VALUES
    (sap_team_id, 'SAP Functional Team', 'Core SAP functional consultants handling business process implementation', admin_user_id, true),
    (technical_team_id, 'SAP Technical Team', 'Technical specialists for SAP development, integration, and security', admin_user_id, true);

    -- Add admin to teams
    INSERT INTO public.team_members (team_id, user_id, role) VALUES
    (sap_team_id, admin_user_id, 'lead'),
    (technical_team_id, admin_user_id, 'lead');

    -- Create SAP implementation projects (using correct enum values)
    INSERT INTO public.projects (id, project_id, name, description, status, created_by, team_id, start_date, end_date, budget, hourly_rate, currency, is_billable) VALUES
    (project1_id, 'SA001', 'Global Manufacturing SAP S/4HANA Implementation', 'End-to-end SAP S/4HANA implementation for global manufacturing company including FI, CO, MM, PP, SD modules with integration to existing systems', 'active', admin_user_id, sap_team_id, '2024-01-15', '2025-06-30', 2500000.00, 250.00, 'USD', true),
    (project2_id, 'SA002', 'Retail Chain SAP Commerce Cloud Integration', 'SAP Commerce Cloud implementation with integration to SAP ERP for omnichannel retail operations', 'planning', admin_user_id, technical_team_id, '2024-03-01', '2024-12-15', 1200000.00, 275.00, 'USD', true),
    (project3_id, 'SA003', 'Financial Services SAP SuccessFactors HCM', 'SAP SuccessFactors Employee Central and Performance Management implementation for financial services company', 'active', admin_user_id, sap_team_id, '2024-02-01', '2024-11-30', 850000.00, 225.00, 'USD', true);

    -- Create project phases for SAP S/4HANA project
    INSERT INTO public.phases (id, project_id, name, description, status, start_date, end_date, order_index, created_by) VALUES
    (phase1_id, project1_id, 'Project Initiation & Planning', 'Project setup, stakeholder alignment, and detailed planning', 'completed', '2024-01-15', '2024-02-29', 1, admin_user_id),
    (phase2_id, project1_id, 'Business Process Design', 'As-is analysis, to-be design, and gap analysis', 'completed', '2024-03-01', '2024-04-30', 2, admin_user_id),
    (phase3_id, project1_id, 'System Build & Configuration', 'SAP configuration, customization, and development', 'in_progress', '2024-05-01', '2024-09-30', 3, admin_user_id);

    -- Create milestones for SAP projects
    INSERT INTO public.milestones (id, project_id, phase_id, name, description, due_date, is_completed, completed_at, completed_by) VALUES
    (milestone1_id, project1_id, phase1_id, 'Project Charter Approval', 'Signed project charter and scope approval from steering committee', '2024-02-15', true, '2024-02-14', admin_user_id),
    (milestone2_id, project1_id, phase2_id, 'Business Process Design Sign-off', 'Approved business process design documents for all modules', '2024-04-25', true, '2024-04-23', admin_user_id);

    -- Create SAP implementation tasks
    INSERT INTO public.tasks (id, project_id, milestone_id, title, description, status, priority, assigned_to, created_by, due_date, estimated_hours, task_id, is_billable) VALUES
    (task1_id, project1_id, milestone1_id, 'SAP S/4HANA System Architecture Design', 'Design comprehensive system architecture for SAP S/4HANA including hardware sizing, network topology, and integration points', 'completed', 'high', admin_user_id, admin_user_id, '2024-02-10', 40, 'SA001-T001', true),
    (task2_id, project1_id, milestone2_id, 'FI Module Configuration', 'Configure Financial Accounting module including chart of accounts, company codes, and financial statement versions', 'in_progress', 'high', admin_user_id, admin_user_id, '2024-07-15', 80, 'SA001-T002', true),
    (task3_id, project1_id, milestone2_id, 'MM Module Blueprint', 'Create detailed blueprint for Materials Management module including procurement processes and vendor management', 'todo', 'medium', admin_user_id, admin_user_id, '2024-08-30', 60, 'SA001-T003', true);

    -- Create subtasks
    INSERT INTO public.tasks (project_id, parent_task_id, title, description, status, priority, assigned_to, created_by, due_date, estimated_hours, task_id, is_billable) VALUES
    (project1_id, task2_id, 'Chart of Accounts Setup', 'Set up company-specific chart of accounts in SAP FI module', 'completed', 'high', admin_user_id, admin_user_id, '2024-06-15', 16, 'SA001-T002-S01', true),
    (project1_id, task2_id, 'Company Code Configuration', 'Configure company codes and assign to controlling areas', 'in_progress', 'high', admin_user_id, admin_user_id, '2024-07-01', 24, 'SA001-T002-S02', true),
    (project1_id, task3_id, 'Vendor Master Data Template', 'Create vendor master data template and validation rules', 'todo', 'medium', admin_user_id, admin_user_id, '2024-08-15', 20, 'SA001-T003-S01', true);

    -- Create time entries for SAP project
    INSERT INTO public.time_entries (project_id, task_id, user_id, start_time, end_time, duration_minutes, description, hourly_rate, is_billable) VALUES
    (project1_id, task1_id, admin_user_id, '2024-02-01 09:00:00', '2024-02-01 17:00:00', 480, 'System architecture design sessions and documentation', 250.00, true),
    (project1_id, task1_id, admin_user_id, '2024-02-02 09:00:00', '2024-02-02 16:00:00', 420, 'Architecture review and stakeholder presentations', 250.00, true),
    (project1_id, task2_id, admin_user_id, '2024-06-10 08:30:00', '2024-06-10 17:30:00', 540, 'FI module configuration and testing', 250.00, true),
    (project1_id, task2_id, admin_user_id, '2024-06-11 09:00:00', '2024-06-11 16:30:00', 450, 'Chart of accounts validation and documentation', 250.00, true);

    -- Create task comments
    INSERT INTO public.task_comments (task_id, user_id, content) VALUES
    (task1_id, admin_user_id, 'Architecture design completed successfully. All stakeholders have approved the proposed S/4HANA landscape design.'),
    (task2_id, admin_user_id, 'FI module configuration is progressing well. Chart of accounts has been set up according to client requirements.'),
    (task2_id, admin_user_id, 'Need to coordinate with client finance team for final validation of account structures before proceeding with company code setup.');

    -- Create support tickets related to SAP implementation
    INSERT INTO public.support_tickets (ticket_id, title, description, status, priority, created_by, project_id) VALUES
    ('TKT-001', 'SAP S/4HANA Performance Issues in DEV Environment', 'Development environment experiencing slow response times during FI posting transactions. Need immediate investigation and optimization.', 'in_progress', 'high', admin_user_id, project1_id),
    ('TKT-002', 'Data Migration Issue - Vendor Master Records', 'Legacy vendor master data migration failing validation. Multiple vendor records have incorrect tax classifications.', 'open', 'high', admin_user_id, project1_id),
    ('TKT-003', 'SAP GUI Connectivity Problems', 'End users reporting intermittent SAP GUI connection drops during training sessions. Network team investigation required.', 'open', 'medium', admin_user_id, project1_id),
    ('TKT-004', 'Integration Testing - Third Party System', 'Integration between SAP and existing warehouse management system showing data inconsistencies.', 'open', 'high', admin_user_id, project1_id);

    -- Create file attachments for SAP project
    INSERT INTO public.file_attachments (project_id, task_id, file_name, file_url, file_type, file_size, uploaded_by) VALUES
    (project1_id, task1_id, 'SAP_S4HANA_Architecture_Design_v2.1.pdf', '/files/sap_architecture_design.pdf', 'application/pdf', 2048576, admin_user_id),
    (project1_id, task2_id, 'FI_Module_Configuration_Guide.docx', '/files/fi_config_guide.docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 1536000, admin_user_id),
    (project1_id, null, 'Project_Charter_SAP_S4HANA_v1.0.pdf', '/files/project_charter.pdf', 'application/pdf', 1024000, admin_user_id),
    (project2_id, null, 'SAP_Commerce_Cloud_Technical_Spec.xlsx', '/files/commerce_cloud_spec.xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 2048000, admin_user_id);

    -- Create project activity logs
    INSERT INTO public.project_activity_logs (project_id, user_id, activity_type, entity_type, entity_id, description, metadata) VALUES
    (project1_id, admin_user_id, 'project_created', 'project', project1_id, 'Created SAP S/4HANA implementation project', '{"project_value": 2500000, "duration_months": 18}'),
    (project1_id, admin_user_id, 'milestone_completed', 'milestone', milestone1_id, 'Completed Project Charter Approval milestone', '{"milestone_name": "Project Charter Approval", "completed_on_time": true}'),
    (project1_id, admin_user_id, 'task_completed', 'task', task1_id, 'Completed System Architecture Design task', '{"task_title": "SAP S/4HANA System Architecture Design", "hours_logged": 15}'),
    (project1_id, admin_user_id, 'phase_started', 'phase', phase3_id, 'Started System Build & Configuration phase', '{"phase_name": "System Build & Configuration", "estimated_duration_months": 5}');

END $$;