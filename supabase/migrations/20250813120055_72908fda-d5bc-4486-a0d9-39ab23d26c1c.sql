-- Clear existing data
DELETE FROM time_entries;
DELETE FROM task_comments;
DELETE FROM file_attachments;
DELETE FROM tasks;
DELETE FROM milestones;
DELETE FROM phases;
DELETE FROM team_members;
DELETE FROM teams;
DELETE FROM projects;
DELETE FROM support_tickets;
DELETE FROM notifications;
DELETE FROM project_activity_logs;
DELETE FROM project_status_updates;

-- Keep the admin user but remove other user roles except admin
DELETE FROM user_roles WHERE role != 'admin';

-- Get the admin user ID (from existing admin users)
DO $$
DECLARE
    admin_user_id UUID;
BEGIN
    -- Get the first admin user
    SELECT ur.user_id INTO admin_user_id 
    FROM user_roles ur 
    WHERE ur.role = 'admin' 
    LIMIT 1;
    
    -- If no admin user found, get the first user and make them admin
    IF admin_user_id IS NULL THEN
        SELECT user_id INTO admin_user_id FROM profiles LIMIT 1;
        
        IF admin_user_id IS NOT NULL THEN
            INSERT INTO user_roles (user_id, role) VALUES (admin_user_id, 'admin') ON CONFLICT (user_id, role) DO NOTHING;
        END IF;
    END IF;

    -- Insert sample SAP implementation projects using the admin user
    INSERT INTO projects (id, name, project_id, description, status, created_by, start_date, end_date, budget, hourly_rate, is_billable, currency) VALUES
    ('********-1111-1111-1111-********1111', 'SAP S/4HANA Finance Implementation', 'SA001', 'Complete SAP S/4HANA Finance module implementation for enterprise client', 'active', admin_user_id, '2024-01-15', '2024-08-15', 500000, 150, true, 'USD'),
    ('********-2222-2222-2222-********2222', 'SAP SuccessFactors HR Transformation', 'SA002', 'SAP SuccessFactors implementation for global HR processes', 'active', admin_user_id, '2024-02-01', '2024-09-30', 350000, 140, true, 'USD'),
    ('33333333-3333-3333-3333-************', 'SAP Ariba Procurement Platform', 'SA003', 'SAP Ariba implementation for procurement and sourcing optimization', 'planning', admin_user_id, '2024-04-01', '2024-12-15', 275000, 135, true, 'USD'),
    ('44444444-4444-4444-4444-************', 'SAP Concur Expense Management', 'SA004', 'SAP Concur implementation for travel and expense management', 'completed', admin_user_id, '2023-09-01', '2024-01-31', 180000, 130, true, 'USD');

    -- Insert sample teams
    INSERT INTO teams (id, name, description, created_by, is_active) VALUES
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'SAP Finance Team', 'Specialized team for SAP Finance implementations', admin_user_id, true),
    ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'SAP HR Team', 'Expert team for SAP HR and SuccessFactors projects', admin_user_id, true),
    ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'SAP Procurement Team', 'Procurement and sourcing specialists', admin_user_id, true);

    -- Update projects with team assignments
    UPDATE projects SET team_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa' WHERE id = '********-1111-1111-1111-********1111';
    UPDATE projects SET team_id = 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb' WHERE id = '********-2222-2222-2222-********2222';
    UPDATE projects SET team_id = 'cccccccc-cccc-cccc-cccc-cccccccccccc' WHERE id = '33333333-3333-3333-3333-************';
    UPDATE projects SET team_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa' WHERE id = '44444444-4444-4444-4444-************';

    -- Add admin user to all teams
    INSERT INTO team_members (team_id, user_id, role) VALUES
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', admin_user_id, 'lead'),
    ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', admin_user_id, 'lead'),
    ('cccccccc-cccc-cccc-cccc-cccccccccccc', admin_user_id, 'lead');

    -- Insert phases for SAP S/4HANA Finance project
    INSERT INTO phases (id, project_id, name, description, status, start_date, end_date, order_index, created_by) VALUES
    ('f1111111-1111-1111-1111-********1111', '********-1111-1111-1111-********1111', 'Discovery & Planning', 'Requirements gathering and project planning phase', 'completed', '2024-01-15', '2024-02-28', 1, admin_user_id),
    ('f2222222-2222-2222-2222-********2222', '********-1111-1111-1111-********1111', 'Design & Configuration', 'System design and initial configuration', 'active', '2024-03-01', '2024-05-15', 2, admin_user_id),
    ('f3333333-3333-3333-3333-************', '********-1111-1111-1111-********1111', 'Development & Testing', 'Custom development and system testing', 'planning', '2024-05-16', '2024-07-15', 3, admin_user_id),
    ('f4444444-4444-4444-4444-************', '********-1111-1111-1111-********1111', 'Go-Live & Support', 'Production deployment and post go-live support', 'planning', '2024-07-16', '2024-08-15', 4, admin_user_id);

    -- Insert milestones for SAP S/4HANA Finance project
    INSERT INTO milestones (id, project_id, phase_id, name, description, due_date, is_completed, completed_by, completed_at) VALUES
    ('m1111111-1111-1111-1111-********1111', '********-1111-1111-1111-********1111', 'f1111111-1111-1111-1111-********1111', 'Requirements Document Approved', 'Complete business requirements documentation', '2024-02-15', true, admin_user_id, '2024-02-14'),
    ('m2222222-2222-2222-2222-********2222', '********-1111-1111-1111-********1111', 'f1111111-1111-1111-1111-********1111', 'Project Charter Signed', 'Project charter approval and sign-off', '2024-02-28', true, admin_user_id, '2024-02-27'),
    ('m3333333-3333-3333-3333-************', '********-1111-1111-1111-********1111', 'f2222222-2222-2222-2222-********2222', 'System Design Complete', 'Technical design and architecture finalized', '2024-04-15', false, null, null),
    ('m4444444-4444-4444-4444-************', '********-1111-1111-1111-********1111', 'f2222222-2222-2222-2222-********2222', 'Configuration Baseline', 'Initial system configuration completed', '2024-05-15', false, null, null);

    -- Insert tasks for SAP S/4HANA Finance project
    INSERT INTO tasks (id, project_id, milestone_id, task_id, title, description, status, priority, assigned_to, created_by, due_date, estimated_hours, actual_hours, is_billable) VALUES
    ('t1111111-1111-1111-1111-********1111', '********-1111-1111-1111-********1111', 'm1111111-1111-1111-1111-********1111', 'SA001-T001', 'Conduct Stakeholder Interviews', 'Interview key stakeholders to gather business requirements', 'completed', 'high', admin_user_id, admin_user_id, '2024-02-10', 40, 38, true),
    ('t2222222-2222-2222-2222-********2222', '********-1111-1111-1111-********1111', 'm1111111-1111-1111-1111-********1111', 'SA001-T002', 'Document Current State Process', 'Map and document existing financial processes', 'completed', 'medium', admin_user_id, admin_user_id, '2024-02-15', 32, 35, true),
    ('t3333333-3333-3333-3333-************', '********-1111-1111-1111-********1111', 'm3333333-3333-3333-3333-************', 'SA001-T003', 'Design Chart of Accounts Structure', 'Create and validate chart of accounts for S/4HANA', 'in_progress', 'high', admin_user_id, admin_user_id, '2024-04-10', 24, 12, true),
    ('t4444444-4444-4444-4444-************', '********-1111-1111-1111-********1111', 'm3333333-3333-3333-3333-************', 'SA001-T004', 'Configure Financial Accounting Module', 'Set up basic FI configuration in SAP S/4HANA', 'todo', 'high', admin_user_id, admin_user_id, '2024-04-20', 56, 0, true),
    ('t5555555-5555-5555-5555-************', '********-1111-1111-1111-********1111', 'm4444444-4444-4444-4444-************', 'SA001-T005', 'Implement Credit Management', 'Configure credit management functionality', 'todo', 'medium', admin_user_id, admin_user_id, '2024-05-05', 28, 0, true);

    -- Insert subtasks
    INSERT INTO tasks (id, project_id, milestone_id, parent_task_id, task_id, title, description, status, priority, assigned_to, created_by, due_date, estimated_hours, actual_hours, is_billable) VALUES
    ('s1111111-1111-1111-1111-********1111', '********-1111-1111-1111-********1111', 'm1111111-1111-1111-1111-********1111', 't1111111-1111-1111-1111-********1111', 'SA001-T001-S01', 'Prepare Interview Questions', 'Create structured interview questionnaire', 'completed', 'medium', admin_user_id, admin_user_id, '2024-02-05', 8, 6, true),
    ('s2222222-2222-2222-2222-********2222', '********-1111-1111-1111-********1111', 'm1111111-1111-1111-1111-********1111', 't1111111-1111-1111-1111-********1111', 'SA001-T001-S02', 'Schedule Stakeholder Meetings', 'Coordinate and schedule all stakeholder interviews', 'completed', 'low', admin_user_id, admin_user_id, '2024-02-07', 4, 3, true),
    ('s3333333-3333-3333-3333-************', '********-1111-1111-1111-********1111', 'm3333333-3333-3333-3333-************', 't3333333-3333-3333-3333-************', 'SA001-T003-S01', 'Analyze Current Chart of Accounts', 'Review existing chart of accounts structure', 'completed', 'high', admin_user_id, admin_user_id, '2024-03-25', 12, 10, true),
    ('s4444444-4444-4444-4444-************', '********-1111-1111-1111-********1111', 'm3333333-3333-3333-3333-************', 't3333333-3333-3333-3333-************', 'SA001-T003-S02', 'Design New Chart Structure', 'Create optimized chart of accounts for S/4HANA', 'in_progress', 'high', admin_user_id, admin_user_id, '2024-04-05', 12, 2, true);

    -- Insert time entries based on task progress
    INSERT INTO time_entries (id, user_id, project_id, task_id, description, start_time, end_time, duration_minutes, is_billable, hourly_rate) VALUES
    ('te111111-1111-1111-1111-********1111', admin_user_id, '********-1111-1111-1111-********1111', 't1111111-1111-1111-1111-********1111', 'Stakeholder interview session 1', '2024-02-05 09:00:00+00', '2024-02-05 17:00:00+00', 480, true, 150),
    ('te222222-2222-2222-2222-********2222', admin_user_id, '********-1111-1111-1111-********1111', 't1111111-1111-1111-1111-********1111', 'Stakeholder interview session 2', '2024-02-06 09:00:00+00', '2024-02-06 17:00:00+00', 480, true, 150),
    ('te333333-3333-3333-3333-************', admin_user_id, '********-1111-1111-1111-********1111', 't2222222-2222-2222-2222-********2222', 'Process documentation work', '2024-02-12 08:00:00+00', '2024-02-12 18:00:00+00', 600, true, 150),
    ('te444444-4444-4444-4444-************', admin_user_id, '********-1111-1111-1111-********1111', 't3333333-3333-3333-3333-************', 'Chart of accounts analysis', '2024-03-20 09:00:00+00', '2024-03-20 15:00:00+00', 360, true, 150),
    ('te555555-5555-5555-5555-************', admin_user_id, '********-1111-1111-1111-********1111', 't3333333-3333-3333-3333-************', 'Chart design work', '2024-03-25 10:00:00+00', '2024-03-25 16:00:00+00', 360, true, 150);

    -- Insert support tickets
    INSERT INTO support_tickets (id, ticket_id, title, description, status, priority, created_by, project_id) VALUES
    ('st111111-1111-1111-1111-********1111', 'TKT-001', 'SAP System Performance Issue', 'Slow response time in FI module during peak hours', 'open', 'high', admin_user_id, '********-1111-1111-1111-********1111'),
    ('st222222-2222-2222-2222-********2222', 'TKT-002', 'Chart of Accounts Validation', 'Need validation for proposed chart of accounts structure', 'in_progress', 'medium', admin_user_id, '********-1111-1111-1111-********1111'),
    ('st333333-3333-3333-3333-************', 'TKT-003', 'User Access Request', 'Request for additional SAP module access permissions', 'resolved', 'low', admin_user_id, '********-2222-2222-2222-********2222');

    -- Create additional phases, milestones, and tasks for SuccessFactors
    INSERT INTO phases (id, project_id, name, description, status, start_date, end_date, order_index, created_by) VALUES
    ('h1111111-1111-1111-1111-********1111', '********-2222-2222-2222-********2222', 'Requirements & Analysis', 'HR business requirements and gap analysis', 'completed', '2024-02-01', '2024-03-15', 1, admin_user_id),
    ('h2222222-2222-2222-2222-********2222', '********-2222-2222-2222-********2222', 'System Configuration', 'SuccessFactors module configuration', 'active', '2024-03-16', '2024-06-30', 2, admin_user_id);

    INSERT INTO milestones (id, project_id, phase_id, name, description, due_date, is_completed) VALUES
    ('mh111111-1111-1111-1111-********1111', '********-2222-2222-2222-********2222', 'h1111111-1111-1111-1111-********1111', 'HR Processes Documented', 'Current state HR processes mapped', '2024-03-01', true),
    ('mh222222-2222-2222-2222-********2222', '********-2222-2222-2222-********2222', 'h2222222-2222-2222-2222-********2222', 'Employee Central Setup', 'Core employee data structure configured', '2024-05-15', false);

    INSERT INTO tasks (id, project_id, milestone_id, task_id, title, description, status, priority, assigned_to, created_by, due_date, estimated_hours, is_billable) VALUES
    ('th111111-1111-1111-1111-********1111', '********-2222-2222-2222-********2222', 'mh111111-1111-1111-1111-********1111', 'SA002-T001', 'Conduct HR Stakeholder Workshops', 'Facilitate workshops with HR teams globally', 'completed', 'high', admin_user_id, admin_user_id, '2024-02-28', 48, true),
    ('th222222-2222-2222-2222-********2222', '********-2222-2222-2222-********2222', 'mh222222-2222-2222-2222-********2222', 'SA002-T002', 'Configure Employee Central', 'Set up employee master data structure', 'in_progress', 'high', admin_user_id, admin_user_id, '2024-05-10', 64, true);

END $$;