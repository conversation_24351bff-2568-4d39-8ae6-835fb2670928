-- Add sample data to demonstrate the project hierarchy
-- This will help users see how Project > Phases > Milestones > Tasks works

-- First, let's add a sample phase for the existing project
INSERT INTO public.phases (
    project_id,
    name,
    description,
    status,
    start_date,
    end_date,
    order_index
)
SELECT 
    p.id,
    'Planning Phase',
    'Initial planning and setup phase for the project',
    'in_progress',
    CURRENT_DATE,
    CURRENT_DATE + INTERVAL '30 days',
    1
FROM public.projects p 
WHERE p.project_id = 'FI001'
ON CONFLICT DO NOTHING;

-- Add another sample phase
INSERT INTO public.phases (
    project_id,
    name,
    description,
    status,
    start_date,
    end_date,
    order_index
)
SELECT 
    p.id,
    'Development Phase',
    'Core development and implementation phase',
    'planning',
    CURRENT_DATE + INTERVAL '31 days',
    CURRENT_DATE + INTERVAL '90 days',
    2
FROM public.projects p 
WHERE p.project_id = 'FI001'
ON CONFLICT DO NOTHING;

-- Add sample milestones for the planning phase
INSERT INTO public.milestones (
    project_id,
    phase_id,
    name,
    description,
    due_date,
    is_completed
)
SELECT 
    p.id as project_id,
    ph.id as phase_id,
    'Requirements Gathering',
    'Complete gathering and documentation of project requirements',
    CURRENT_DATE + INTERVAL '10 days',
    false
FROM public.projects p 
JOIN public.phases ph ON ph.project_id = p.id
WHERE p.project_id = 'FI001' AND ph.name = 'Planning Phase'
ON CONFLICT DO NOTHING;

INSERT INTO public.milestones (
    project_id,
    phase_id,
    name,
    description,
    due_date,
    is_completed
)
SELECT 
    p.id as project_id,
    ph.id as phase_id,
    'Technical Architecture',
    'Define technical architecture and system design',
    CURRENT_DATE + INTERVAL '20 days',
    false
FROM public.projects p 
JOIN public.phases ph ON ph.project_id = p.id
WHERE p.project_id = 'FI001' AND ph.name = 'Planning Phase'
ON CONFLICT DO NOTHING;

-- Add sample milestones for the development phase
INSERT INTO public.milestones (
    project_id,
    phase_id,
    name,
    description,
    due_date,
    is_completed
)
SELECT 
    p.id as project_id,
    ph.id as phase_id,
    'Core Development',
    'Complete core system development',
    CURRENT_DATE + INTERVAL '60 days',
    false
FROM public.projects p 
JOIN public.phases ph ON ph.project_id = p.id
WHERE p.project_id = 'FI001' AND ph.name = 'Development Phase'
ON CONFLICT DO NOTHING;

-- Add sample tasks for the milestones
INSERT INTO public.tasks (
    project_id,
    milestone_id,
    task_id,
    title,
    description,
    status,
    priority,
    due_date,
    created_by
)
SELECT 
    p.id as project_id,
    m.id as milestone_id,
    'FI001-T001',
    'Document User Stories',
    'Create detailed user stories for all system features',
    'todo',
    'high',
    CURRENT_DATE + INTERVAL '7 days',
    p.created_by
FROM public.projects p 
JOIN public.phases ph ON ph.project_id = p.id
JOIN public.milestones m ON m.phase_id = ph.id
WHERE p.project_id = 'FI001' AND ph.name = 'Planning Phase' AND m.name = 'Requirements Gathering'
ON CONFLICT (task_id) DO NOTHING;

INSERT INTO public.tasks (
    project_id,
    milestone_id,
    task_id,
    title,
    description,
    status,
    priority,
    due_date,
    created_by
)
SELECT 
    p.id as project_id,
    m.id as milestone_id,
    'FI001-T002',
    'System Analysis',
    'Analyze current system and identify integration points',
    'todo',
    'medium',
    CURRENT_DATE + INTERVAL '8 days',
    p.created_by
FROM public.projects p 
JOIN public.phases ph ON ph.project_id = p.id
JOIN public.milestones m ON m.phase_id = ph.id
WHERE p.project_id = 'FI001' AND ph.name = 'Planning Phase' AND m.name = 'Requirements Gathering'
ON CONFLICT (task_id) DO NOTHING;

INSERT INTO public.tasks (
    project_id,
    milestone_id,
    task_id,
    title,
    description,
    status,
    priority,
    due_date,
    created_by
)
SELECT 
    p.id as project_id,
    m.id as milestone_id,
    'FI001-T003',
    'Database Design',
    'Design database schema and relationships',
    'in_progress',
    'high',
    CURRENT_DATE + INTERVAL '15 days',
    p.created_by
FROM public.projects p 
JOIN public.phases ph ON ph.project_id = p.id
JOIN public.milestones m ON m.phase_id = ph.id
WHERE p.project_id = 'FI001' AND ph.name = 'Planning Phase' AND m.name = 'Technical Architecture'
ON CONFLICT (task_id) DO NOTHING;