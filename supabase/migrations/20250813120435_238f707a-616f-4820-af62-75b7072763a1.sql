-- Drop all existing triggers for auto_create_notifications
DROP TRIGGER IF EXISTS task_status_notifications ON tasks;
DROP TRIGGER IF EXISTS ticket_assignment_notifications ON support_tickets;

-- Recreate the function with proper type handling
CREATE OR REPLACE FUNCTION public.auto_create_notifications()
 <PERSON><PERSON><PERSON><PERSON> trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    -- Task assignment notifications
    IF TG_TABLE_NAME = 'tasks' AND TG_OP = 'UPDATE' AND OLD.assigned_to IS DISTINCT FROM NEW.assigned_to AND NEW.assigned_to IS NOT NULL THEN
        PERFORM create_notification(
            NEW.assigned_to,
            'New Task Assigned',
            'You have been assigned to task "' || NEW.title || '"',
            'info',
            '/projects/' || NEW.project_id::text
        );
    END IF;
    
    -- Task completion notifications - compare as text to avoid enum issues
    IF TG_TABLE_NAME = 'tasks' AND TG_OP = 'UPDATE' AND OLD.status::text != NEW.status::text AND NEW.status::text = 'completed' THEN
        PERFORM create_notification(
            (SELECT created_by FROM projects WHERE id = NEW.project_id),
            'Task Completed',
            'Task "' || NEW.title || '" has been completed',
            'success',
            '/projects/' || NEW.project_id::text
        );
    END IF;
    
    -- Support ticket assignment notifications
    IF TG_TABLE_NAME = 'support_tickets' AND TG_OP = 'UPDATE' AND OLD.assigned_to IS DISTINCT FROM NEW.assigned_to AND NEW.assigned_to IS NOT NULL THEN
        PERFORM create_notification(
            NEW.assigned_to,
            'Support Ticket Assigned',
            'You have been assigned to ticket "' || NEW.title || '"',
            'warning',
            '/support-tickets'
        );
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$function$;

-- Recreate triggers
CREATE TRIGGER task_notifications
AFTER UPDATE ON tasks
FOR EACH ROW
EXECUTE FUNCTION auto_create_notifications();

CREATE TRIGGER ticket_notifications  
AFTER UPDATE ON support_tickets
FOR EACH ROW
EXECUTE FUNCTION auto_create_notifications();

-- Now run the sample data insertion with simple approach
-- Clear existing data
DELETE FROM time_entries;
DELETE FROM task_comments;
DELETE FROM file_attachments;
DELETE FROM tasks;
DELETE FROM milestones;
DELETE FROM phases;
DELETE FROM team_members;
DELETE FROM teams;
DELETE FROM projects;
DELETE FROM support_tickets;
DELETE FROM notifications;
DELETE FROM project_activity_logs;
DELETE FROM project_status_updates;