-- Add sample client users for testing project creation
DO $$
DECLARE
    client1_id uuid := gen_random_uuid();
    client2_id uuid := gen_random_uuid();
    client3_id uuid := gen_random_uuid();
    admin_user_id uuid := '183d5d61-4b48-4f60-b4a3-1cc45d76e713';
BEGIN
    -- Create sample client profiles
    INSERT INTO public.profiles (user_id, first_name, last_name, email, position, department, is_active) VALUES
    (client1_id, 'John', '<PERSON>', '<EMAIL>', 'IT Director', 'Information Technology', true),
    (client2_id, '<PERSON>', '<PERSON>', '<EMAIL>', 'Chief Technology Officer', 'Technology', true),
    (client3_id, 'David', '<PERSON>', '<EMAIL>', 'VP of Operations', 'Operations', true);

    -- Assign client roles to these users
    INSERT INTO public.user_roles (user_id, role, assigned_by) VALUES
    (client1_id, 'client', admin_user_id),
    (client2_id, 'client', admin_user_id),
    (client3_id, 'client', admin_user_id);

    -- Update existing projects to have client assignments
    UPDATE public.projects 
    SET client_id = client1_id 
    WHERE project_id = 'SA001';

    UPDATE public.projects 
    SET client_id = client2_id 
    WHERE project_id = 'SA002';

    UPDATE public.projects 
    SET client_id = client3_id 
    WHERE project_id = 'SA003';

END $$;
