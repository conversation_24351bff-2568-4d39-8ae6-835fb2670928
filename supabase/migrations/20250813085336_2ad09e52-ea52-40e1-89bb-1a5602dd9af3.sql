-- Create phases table
CREATE TABLE public.phases (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    start_date DATE,
    end_date DATE,
    status TEXT NOT NULL DEFAULT 'planning',
    order_index INTEGER DEFAULT 0,
    created_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    CONSTRAINT phases_status_check CHECK (status IN ('planning', 'in_progress', 'completed', 'on_hold'))
);

-- Enable RLS on phases
ALTER TABLE public.phases ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for phases
CREATE POLICY "Project stakeholders can manage phases" 
ON public.phases 
FOR ALL 
USING (
    is_admin(auth.uid()) OR 
    is_project_manager(auth.uid()) OR 
    (EXISTS (
        SELECT 1 FROM projects p 
        WHERE p.id = phases.project_id 
        AND (
            p.created_by = auth.uid() OR 
            EXISTS (
                SELECT 1 FROM team_members tm 
                WHERE tm.team_id = p.team_id 
                AND tm.user_id = auth.uid()
            )
        )
    ))
)
WITH CHECK (
    is_admin(auth.uid()) OR 
    is_project_manager(auth.uid()) OR 
    (EXISTS (
        SELECT 1 FROM projects p 
        WHERE p.id = phases.project_id 
        AND (
            p.created_by = auth.uid() OR 
            EXISTS (
                SELECT 1 FROM team_members tm 
                WHERE tm.team_id = p.team_id 
                AND tm.user_id = auth.uid()
            )
        )
    ))
);

-- Add trigger for automatic timestamp updates
CREATE TRIGGER update_phases_updated_at
BEFORE UPDATE ON public.phases
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Update milestones table to reference phases instead of projects directly
ALTER TABLE public.milestones ADD COLUMN phase_id UUID;

-- Create a foreign key relationship (optional, but good practice)
-- ALTER TABLE public.milestones ADD CONSTRAINT fk_milestones_phase_id 
-- FOREIGN KEY (phase_id) REFERENCES public.phases(id) ON DELETE CASCADE;

-- For existing milestones, we'll need to create default phases
-- This is a data migration script to handle existing data
DO $$
DECLARE
    project_record RECORD;
    phase_id UUID;
BEGIN
    -- For each project that has milestones but no phases
    FOR project_record IN 
        SELECT DISTINCT p.id as project_id, p.created_by, p.name as project_name
        FROM projects p
        INNER JOIN milestones m ON m.project_id = p.id
        WHERE NOT EXISTS (
            SELECT 1 FROM phases ph WHERE ph.project_id = p.id
        )
    LOOP
        -- Create a default "Main Phase" for this project
        INSERT INTO public.phases (
            project_id, 
            name, 
            description, 
            status, 
            created_by,
            order_index
        ) VALUES (
            project_record.project_id,
            'Main Phase',
            'Default phase created during system migration',
            'in_progress',
            project_record.created_by,
            1
        ) RETURNING id INTO phase_id;
        
        -- Update all milestones for this project to reference the new phase
        UPDATE public.milestones 
        SET phase_id = phase_id 
        WHERE project_id = project_record.project_id;
    END LOOP;
END $$;