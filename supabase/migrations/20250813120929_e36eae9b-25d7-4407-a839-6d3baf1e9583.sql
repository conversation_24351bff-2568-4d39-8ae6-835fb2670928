-- Sample data insertion with proper UUIDs
INSERT INTO projects (name, project_id, description, status, created_by, start_date, end_date, budget, hourly_rate, is_billable, currency) VALUES
('SAP S/4HANA Finance Implementation', 'SA001', 'Complete SAP S/4HANA Finance module implementation for enterprise client', 'active', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-01-15', '2024-08-15', 500000, 150, true, 'USD'),
('SAP SuccessFactors HR Transformation', 'SA002', 'SAP SuccessFactors implementation for global HR processes', 'active', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-02-01', '2024-09-30', 350000, 140, true, 'USD'),
('SAP Ariba Procurement Platform', 'SA003', 'SAP Ariba implementation for procurement and sourcing optimization', 'planning', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-04-01', '2024-12-15', 275000, 135, true, 'USD');

-- Get the project IDs that were just created
DO $$
DECLARE
    project_1_id UUID;
    project_2_id UUID;
    project_3_id UUID;
    team_1_id UUID;
    team_2_id UUID;
    phase_1_id UUID;
    phase_2_id UUID;
    milestone_1_id UUID;
    milestone_2_id UUID;
    task_1_id UUID;
    task_2_id UUID;
    task_3_id UUID;
BEGIN
    -- Get project IDs
    SELECT id INTO project_1_id FROM projects WHERE project_id = 'SA001';
    SELECT id INTO project_2_id FROM projects WHERE project_id = 'SA002';
    SELECT id INTO project_3_id FROM projects WHERE project_id = 'SA003';
    
    -- Insert teams
    INSERT INTO teams (name, description, created_by, is_active) VALUES
    ('SAP Finance Team', 'Specialized team for SAP Finance implementations', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', true),
    ('SAP HR Team', 'Expert team for SAP HR and SuccessFactors projects', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', true)
    RETURNING id INTO team_1_id;
    
    -- Get team IDs
    SELECT id INTO team_1_id FROM teams WHERE name = 'SAP Finance Team';
    SELECT id INTO team_2_id FROM teams WHERE name = 'SAP HR Team';
    
    -- Update projects with team assignments  
    UPDATE projects SET team_id = team_1_id WHERE id = project_1_id;
    UPDATE projects SET team_id = team_2_id WHERE id = project_2_id;
    
    -- Add user to teams
    INSERT INTO team_members (team_id, user_id, role) VALUES
    (team_1_id, '183d5d61-4b48-4f60-b4a3-1cc45d76e713', 'lead'),
    (team_2_id, '183d5d61-4b48-4f60-b4a3-1cc45d76e713', 'lead');
    
    -- Insert phases
    INSERT INTO phases (project_id, name, description, status, start_date, end_date, order_index, created_by) VALUES
    (project_1_id, 'Discovery & Planning', 'Requirements gathering and project planning phase', 'completed', '2024-01-15', '2024-02-28', 1, '183d5d61-4b48-4f60-b4a3-1cc45d76e713'),
    (project_1_id, 'Design & Configuration', 'System design and initial configuration', 'in_progress', '2024-03-01', '2024-05-15', 2, '183d5d61-4b48-4f60-b4a3-1cc45d76e713')
    RETURNING id INTO phase_1_id;
    
    -- Get phase IDs
    SELECT id INTO phase_1_id FROM phases WHERE name = 'Discovery & Planning' AND project_id = project_1_id;
    SELECT id INTO phase_2_id FROM phases WHERE name = 'Design & Configuration' AND project_id = project_1_id;
    
    -- Insert milestones
    INSERT INTO milestones (project_id, phase_id, name, description, due_date, is_completed, completed_by, completed_at) VALUES
    (project_1_id, phase_1_id, 'Requirements Document Approved', 'Complete business requirements documentation', '2024-02-15', true, '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-02-14'),
    (project_1_id, phase_2_id, 'System Design Complete', 'Technical design and architecture finalized', '2024-04-15', false, null, null)
    RETURNING id INTO milestone_1_id;
    
    -- Get milestone IDs
    SELECT id INTO milestone_1_id FROM milestones WHERE name = 'Requirements Document Approved' AND project_id = project_1_id;
    SELECT id INTO milestone_2_id FROM milestones WHERE name = 'System Design Complete' AND project_id = project_1_id;
    
    -- Insert tasks
    INSERT INTO tasks (project_id, milestone_id, task_id, title, description, status, priority, assigned_to, created_by, due_date, estimated_hours, actual_hours, is_billable) VALUES
    (project_1_id, milestone_1_id, 'SA001-T001', 'Conduct Stakeholder Interviews', 'Interview key stakeholders to gather business requirements', 'completed', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-02-10', 40, 38, true),
    (project_1_id, milestone_2_id, 'SA001-T002', 'Design Chart of Accounts Structure', 'Create and validate chart of accounts for S/4HANA', 'in_progress', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-04-10', 24, 12, true),
    (project_1_id, milestone_2_id, 'SA001-T003', 'Configure Financial Accounting Module', 'Set up basic FI configuration in SAP S/4HANA', 'todo', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-04-20', 56, 0, true)
    RETURNING id INTO task_1_id;
    
    -- Get task IDs
    SELECT id INTO task_1_id FROM tasks WHERE task_id = 'SA001-T001';
    SELECT id INTO task_2_id FROM tasks WHERE task_id = 'SA001-T002';
    SELECT id INTO task_3_id FROM tasks WHERE task_id = 'SA001-T003';
    
    -- Insert subtasks
    INSERT INTO tasks (project_id, milestone_id, parent_task_id, task_id, title, description, status, priority, assigned_to, created_by, due_date, estimated_hours, actual_hours, is_billable) VALUES
    (project_1_id, milestone_1_id, task_1_id, 'SA001-T001-S01', 'Prepare Interview Questions', 'Create structured interview questionnaire', 'completed', 'medium', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-02-05', 8, 6, true),
    (project_1_id, milestone_2_id, task_2_id, 'SA001-T002-S01', 'Analyze Current Chart of Accounts', 'Review existing chart of accounts structure', 'completed', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-03-25', 12, 10, true);
    
    -- Insert time entries
    INSERT INTO time_entries (user_id, project_id, task_id, description, start_time, end_time, duration_minutes, is_billable, hourly_rate) VALUES
    ('183d5d61-4b48-4f60-b4a3-1cc45d76e713', project_1_id, task_1_id, 'Stakeholder interview session 1', '2024-02-05 09:00:00+00', '2024-02-05 17:00:00+00', 480, true, 150),
    ('183d5d61-4b48-4f60-b4a3-1cc45d76e713', project_1_id, task_2_id, 'Chart design work', '2024-03-25 10:00:00+00', '2024-03-25 16:00:00+00', 360, true, 150);
    
    -- Insert support tickets
    INSERT INTO support_tickets (ticket_id, title, description, status, priority, created_by, project_id) VALUES
    ('TKT-001', 'SAP System Performance Issue', 'Slow response time in FI module during peak hours', 'open', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', project_1_id),
    ('TKT-002', 'Chart of Accounts Validation', 'Need validation for proposed chart of accounts structure', 'in_progress', 'medium', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', project_1_id);
    
END $$;