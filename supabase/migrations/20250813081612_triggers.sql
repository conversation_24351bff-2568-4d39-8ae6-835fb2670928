-- Create missing tables for RatioHub
-- Add missing task_comments and project_status_updates tables

-- Create task_comments table for task discussions
CREATE TABLE IF NOT EXISTS public.task_comments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  task_id UUID NOT NULL,
  user_id UUID NOT NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create project_status_updates table for project activity feed
CREATE TABLE IF NOT EXISTS public.project_status_updates (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID NOT NULL,
  user_id UUID NOT NULL,
  update_type TEXT NOT NULL, -- 'status_change', 'task_created', 'milestone_completed', etc.
  title TEXT NOT NULL,
  description TEXT,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on new tables
ALTER TABLE public.task_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_status_updates ENABLE ROW LEVEL SECURITY;

-- Add RLS policies for task_comments
CREATE POLICY "Project stakeholders can manage task comments" 
ON public.task_comments FOR ALL 
USING (
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  user_id = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM tasks t 
    JOIN projects p ON p.id = t.project_id 
    WHERE t.id = task_comments.task_id AND (
      t.assigned_to = auth.uid() OR 
      t.created_by = auth.uid() OR 
      p.created_by = auth.uid() OR 
      EXISTS (SELECT 1 FROM team_members tm WHERE tm.team_id = p.team_id AND tm.user_id = auth.uid())
    )
  )
) 
WITH CHECK (is_admin(auth.uid()) OR is_project_manager(auth.uid()) OR user_id = auth.uid());

-- Add RLS policies for project_status_updates
CREATE POLICY "Project stakeholders can view project updates" 
ON public.project_status_updates FOR SELECT 
USING (
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  user_id = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM projects p 
    WHERE p.id = project_status_updates.project_id AND (
      p.created_by = auth.uid() OR 
      EXISTS (SELECT 1 FROM team_members tm WHERE tm.team_id = p.team_id AND tm.user_id = auth.uid())
    )
  )
);

CREATE POLICY "Project stakeholders can create project updates" 
ON public.project_status_updates FOR INSERT 
WITH CHECK (
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  user_id = auth.uid()
);

-- Add ID generation functions and triggers
-- Project ID Generation Function
CREATE OR REPLACE FUNCTION public.generate_project_id(project_name text)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    prefix TEXT;
    next_number INTEGER;
    new_id TEXT;
    char_count INTEGER := 0;
    i INTEGER := 1;
BEGIN
    -- Extract first 2 alphabetic characters
    prefix := '';
    WHILE char_count < 2 AND i <= length(project_name) LOOP
        IF substring(project_name, i, 1) ~ '[A-Za-z]' THEN
            prefix := prefix || upper(substring(project_name, i, 1));
            char_count := char_count + 1;
        END IF;
        i := i + 1;
    END LOOP;
    
    -- Handle edge case where we don't have 2 alphabetic characters
    IF char_count < 2 THEN
        prefix := prefix || repeat('X', 2 - char_count);
    END IF;
    
    -- Find highest existing number for this prefix
    SELECT COALESCE(MAX(CAST(substring(project_id, 3) AS INTEGER)), 0) + 1
    INTO next_number
    FROM public.projects
    WHERE project_id ~ ('^' || prefix || '[0-9]{3}$');
    
    -- Generate new ID with 3-digit number
    new_id := prefix || lpad(next_number::TEXT, 3, '0');
    
    RETURN new_id;
END;
$$;

-- Task ID Generation Function
CREATE OR REPLACE FUNCTION public.generate_task_id(proj_id uuid)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    project_code TEXT;
    next_number INTEGER;
    new_id TEXT;
BEGIN
    -- Get project code
    SELECT project_id INTO project_code
    FROM public.projects
    WHERE id = proj_id;
    
    -- Find highest existing task number for this project
    SELECT COALESCE(MAX(CAST(substring(task_id, length(project_code) + 3) AS INTEGER)), 0) + 1
    INTO next_number
    FROM public.tasks
    WHERE task_id ~ ('^' || project_code || '-T[0-9]{3}$');
    
    -- Generate new task ID
    new_id := project_code || '-T' || lpad(next_number::TEXT, 3, '0');
    
    RETURN new_id;
END;
$$;

-- Ticket ID Generation Function
CREATE OR REPLACE FUNCTION public.generate_ticket_id()
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    next_number INTEGER;
    new_id TEXT;
BEGIN
    -- Find highest existing ticket number
    SELECT COALESCE(MAX(CAST(substring(ticket_id, 5) AS INTEGER)), 0) + 1
    INTO next_number
    FROM public.support_tickets
    WHERE ticket_id ~ '^TKT-[0-9]{3}$';
    
    -- Generate new ticket ID
    new_id := 'TKT-' || lpad(next_number::TEXT, 3, '0');
    
    RETURN new_id;
END;
$$;

-- Triggers for auto ID generation
CREATE OR REPLACE FUNCTION public.set_project_id()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    IF NEW.project_id IS NULL OR NEW.project_id = '' THEN
        NEW.project_id := public.generate_project_id(NEW.name);
    END IF;
    RETURN NEW;
END;
$$;

CREATE OR REPLACE FUNCTION public.set_task_id()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    IF NEW.task_id IS NULL OR NEW.task_id = '' THEN
        NEW.task_id := public.generate_task_id(NEW.project_id);
    END IF;
    RETURN NEW;
END;
$$;

CREATE OR REPLACE FUNCTION public.set_ticket_id()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    IF NEW.ticket_id IS NULL OR NEW.ticket_id = '' THEN
        NEW.ticket_id := public.generate_ticket_id();
    END IF;
    RETURN NEW;
END;
$$;

-- Create triggers
CREATE TRIGGER trigger_set_project_id
    BEFORE INSERT ON public.projects
    FOR EACH ROW EXECUTE FUNCTION public.set_project_id();

CREATE TRIGGER trigger_set_task_id
    BEFORE INSERT ON public.tasks
    FOR EACH ROW EXECUTE FUNCTION public.set_task_id();

CREATE TRIGGER trigger_set_ticket_id
    BEFORE INSERT ON public.support_tickets
    FOR EACH ROW EXECUTE FUNCTION public.set_ticket_id();

-- Updated timestamp triggers
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_teams_updated_at
    BEFORE UPDATE ON public.teams
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_projects_updated_at
    BEFORE UPDATE ON public.projects
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_milestones_updated_at
    BEFORE UPDATE ON public.milestones
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at
    BEFORE UPDATE ON public.tasks
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_support_tickets_updated_at
    BEFORE UPDATE ON public.support_tickets
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_time_entries_updated_at
    BEFORE UPDATE ON public.time_entries
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_task_comments_updated_at
    BEFORE UPDATE ON public.task_comments
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();