-- Create user invitations table
CREATE TABLE public.user_invitations (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    email TEXT NOT NULL,
    invited_by UUID NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    role app_role NOT NULL DEFAULT 'user',
    team_ids UUID[] DEFAULT NULL,
    invitation_token TEXT NOT NULL DEFAULT encode(gen_random_bytes(32), 'hex'),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (now() + interval '7 days'),
    accepted_at TIMESTAMP WITH TIME ZONE NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    metadata JSONB DEFAULT NULL,
    UNIQUE(email, status) -- Prevent duplicate pending invitations
);

-- Enable RLS
ALTER TABLE public.user_invitations ENABLE ROW LEVEL SECURITY;

-- Create policies for invitations
CREATE POLICY "<PERSON><PERSON> can manage all invitations" 
ON public.user_invitations 
FOR ALL 
USING (is_admin(auth.uid()))
WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Project managers can view invitations they sent"
ON public.user_invitations
FOR SELECT
USING (is_project_manager(auth.uid()) AND invited_by = auth.uid());

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_user_invitations_updated_at
BEFORE UPDATE ON public.user_invitations
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Create function to generate secure invitation tokens
CREATE OR REPLACE FUNCTION public.generate_invitation_token()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    RETURN encode(gen_random_bytes(32), 'hex');
END;
$$;

-- Create function to accept invitation
CREATE OR REPLACE FUNCTION public.accept_invitation(invitation_token TEXT, user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    invitation_record RECORD;
    team_id UUID;
BEGIN
    -- Find valid invitation
    SELECT * INTO invitation_record
    FROM public.user_invitations
    WHERE invitation_token = accept_invitation.invitation_token
    AND status = 'pending'
    AND expires_at > now();
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Update invitation status
    UPDATE public.user_invitations
    SET status = 'accepted',
        accepted_at = now(),
        updated_at = now()
    WHERE invitation_token = accept_invitation.invitation_token;
    
    -- Assign role to user
    INSERT INTO public.user_roles (user_id, role, assigned_by)
    VALUES (user_id, invitation_record.role, invitation_record.invited_by)
    ON CONFLICT (user_id, role) DO NOTHING;
    
    -- Add user to teams if specified
    IF invitation_record.team_ids IS NOT NULL THEN
        FOREACH team_id IN ARRAY invitation_record.team_ids
        LOOP
            INSERT INTO public.team_members (team_id, user_id, role)
            VALUES (team_id, user_id, 'member')
            ON CONFLICT (team_id, user_id) DO NOTHING;
        END LOOP;
    END IF;
    
    RETURN TRUE;
END;
$$;