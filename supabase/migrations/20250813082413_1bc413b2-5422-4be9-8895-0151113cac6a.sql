-- Add missing foreign key constraints to establish proper relationships
-- This will fix the relationship issues causing the project and team detail pages to fail

-- Add foreign key constraints for profiles table
ALTER TABLE public.profiles 
ADD CONSTRAINT profiles_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Add foreign key constraints for projects table
ALTER TABLE public.projects 
ADD CONSTRAINT projects_created_by_fkey 
FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE RESTRICT;

ALTER TABLE public.projects 
ADD CONSTRAINT projects_client_id_fkey 
FOREIGN KEY (client_id) REFERENCES auth.users(id) ON DELETE SET NULL;

ALTER TABLE public.projects 
ADD CONSTRAINT projects_team_id_fkey 
FOREIGN KEY (team_id) REFERENCES public.teams(id) ON DELETE SET NULL;

-- Add foreign key constraints for teams table
ALTER TABLE public.teams 
ADD CONSTRAINT teams_created_by_fkey 
FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE RESTRICT;

-- Add foreign key constraints for team_members table
ALTER TABLE public.team_members 
ADD CONSTRAINT team_members_team_id_fkey 
FOREIGN KEY (team_id) REFERENCES public.teams(id) ON DELETE CASCADE;

ALTER TABLE public.team_members 
ADD CONSTRAINT team_members_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Add foreign key constraints for milestones table
ALTER TABLE public.milestones 
ADD CONSTRAINT milestones_project_id_fkey 
FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;

ALTER TABLE public.milestones 
ADD CONSTRAINT milestones_completed_by_fkey 
FOREIGN KEY (completed_by) REFERENCES auth.users(id) ON DELETE SET NULL;

-- Add foreign key constraints for tasks table
ALTER TABLE public.tasks 
ADD CONSTRAINT tasks_project_id_fkey 
FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;

ALTER TABLE public.tasks 
ADD CONSTRAINT tasks_milestone_id_fkey 
FOREIGN KEY (milestone_id) REFERENCES public.milestones(id) ON DELETE SET NULL;

ALTER TABLE public.tasks 
ADD CONSTRAINT tasks_parent_task_id_fkey 
FOREIGN KEY (parent_task_id) REFERENCES public.tasks(id) ON DELETE CASCADE;

ALTER TABLE public.tasks 
ADD CONSTRAINT tasks_assigned_to_fkey 
FOREIGN KEY (assigned_to) REFERENCES auth.users(id) ON DELETE SET NULL;

ALTER TABLE public.tasks 
ADD CONSTRAINT tasks_created_by_fkey 
FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE RESTRICT;

-- Add foreign key constraints for support_tickets table
ALTER TABLE public.support_tickets 
ADD CONSTRAINT support_tickets_created_by_fkey 
FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE RESTRICT;

ALTER TABLE public.support_tickets 
ADD CONSTRAINT support_tickets_assigned_to_fkey 
FOREIGN KEY (assigned_to) REFERENCES auth.users(id) ON DELETE SET NULL;

ALTER TABLE public.support_tickets 
ADD CONSTRAINT support_tickets_project_id_fkey 
FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE SET NULL;

-- Add foreign key constraints for time_entries table
ALTER TABLE public.time_entries 
ADD CONSTRAINT time_entries_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE public.time_entries 
ADD CONSTRAINT time_entries_project_id_fkey 
FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;

ALTER TABLE public.time_entries 
ADD CONSTRAINT time_entries_task_id_fkey 
FOREIGN KEY (task_id) REFERENCES public.tasks(id) ON DELETE SET NULL;

-- Add foreign key constraints for file_attachments table
ALTER TABLE public.file_attachments 
ADD CONSTRAINT file_attachments_project_id_fkey 
FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;

ALTER TABLE public.file_attachments 
ADD CONSTRAINT file_attachments_task_id_fkey 
FOREIGN KEY (task_id) REFERENCES public.tasks(id) ON DELETE CASCADE;

ALTER TABLE public.file_attachments 
ADD CONSTRAINT file_attachments_uploaded_by_fkey 
FOREIGN KEY (uploaded_by) REFERENCES auth.users(id) ON DELETE RESTRICT;

-- Add foreign key constraints for task_comments table
ALTER TABLE public.task_comments 
ADD CONSTRAINT task_comments_task_id_fkey 
FOREIGN KEY (task_id) REFERENCES public.tasks(id) ON DELETE CASCADE;

ALTER TABLE public.task_comments 
ADD CONSTRAINT task_comments_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE RESTRICT;

-- Add foreign key constraints for project_status_updates table
ALTER TABLE public.project_status_updates 
ADD CONSTRAINT project_status_updates_project_id_fkey 
FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;

ALTER TABLE public.project_status_updates 
ADD CONSTRAINT project_status_updates_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE RESTRICT;

-- Add foreign key constraints for user_roles table
ALTER TABLE public.user_roles 
ADD CONSTRAINT user_roles_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE public.user_roles 
ADD CONSTRAINT user_roles_assigned_by_fkey 
FOREIGN KEY (assigned_by) REFERENCES auth.users(id) ON DELETE SET NULL;

-- Add foreign key constraints for activity_logs table
ALTER TABLE public.activity_logs 
ADD CONSTRAINT activity_logs_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL;

-- Add foreign key constraints for security_events table
ALTER TABLE public.security_events 
ADD CONSTRAINT security_events_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL;