-- Simple sample data insertion
INSERT INTO projects (name, project_id, description, status, created_by) VALUES
('SAP S/4HANA Finance Implementation', 'SA001', 'Complete SAP S/4HANA Finance module implementation', 'active', '183d5d61-4b48-4f60-b4a3-1cc45d76e713');

INSERT INTO teams (name, description, created_by) VALUES
('SAP Finance Team', 'Specialized team for SAP Finance implementations', '183d5d61-4b48-4f60-b4a3-1cc45d76e713');

-- Link project to team
UPDATE projects SET team_id = (SELECT id FROM teams WHERE name = 'SAP Finance Team' LIMIT 1) WHERE project_id = 'SA001';

-- Add user to team
INSERT INTO team_members (team_id, user_id, role) 
SELECT id, '183d5d61-4b48-4f60-b4a3-1cc45d76e713', 'lead' 
FROM teams WHERE name = 'SAP Finance Team';

-- Add a simple phase
INSERT INTO phases (project_id, name, description, status, order_index, created_by)
SELECT id, 'Discovery & Planning', 'Requirements gathering phase', 'completed', 1, '183d5d61-4b48-4f60-b4a3-1cc45d76e713'
FROM projects WHERE project_id = 'SA001';

-- Add a milestone
INSERT INTO milestones (project_id, phase_id, name, description, due_date, is_completed)
SELECT p.id, ph.id, 'Requirements Complete', 'All requirements documented', '2024-02-15', true
FROM projects p, phases ph 
WHERE p.project_id = 'SA001' AND ph.project_id = p.id AND ph.name = 'Discovery & Planning';

-- Add some tasks
INSERT INTO tasks (project_id, milestone_id, task_id, title, description, status, priority, assigned_to, created_by, estimated_hours, actual_hours)
SELECT p.id, m.id, 'SA001-T001', 'Conduct Stakeholder Interviews', 'Interview key stakeholders', 'completed', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', 40, 38
FROM projects p, milestones m 
WHERE p.project_id = 'SA001' AND m.project_id = p.id;

INSERT INTO tasks (project_id, milestone_id, task_id, title, description, status, priority, assigned_to, created_by, estimated_hours)
SELECT p.id, m.id, 'SA001-T002', 'Document Current Processes', 'Map existing processes', 'in_progress', 'medium', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', 24
FROM projects p, milestones m 
WHERE p.project_id = 'SA001' AND m.project_id = p.id;

-- Add time entries
INSERT INTO time_entries (user_id, project_id, task_id, description, start_time, end_time, duration_minutes, is_billable, hourly_rate)
SELECT '183d5d61-4b48-4f60-b4a3-1cc45d76e713', p.id, t.id, 'Interview session', '2024-02-05 09:00:00+00', '2024-02-05 17:00:00+00', 480, true, 150
FROM projects p, tasks t 
WHERE p.project_id = 'SA001' AND t.project_id = p.id AND t.task_id = 'SA001-T001';

-- Add support tickets
INSERT INTO support_tickets (ticket_id, title, description, status, priority, created_by, project_id)
SELECT 'TKT-001', 'Performance Issue', 'System running slow', 'open', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', id
FROM projects WHERE project_id = 'SA001';