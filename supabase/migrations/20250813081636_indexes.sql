-- Create only the missing tables that don't exist
CREATE TABLE IF NOT EXISTS public.task_comments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  task_id UUID NOT NULL,
  user_id UUID NOT NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.project_status_updates (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID NOT NULL,
  user_id UUID NOT NULL,
  update_type TEXT NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS if not already enabled
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'task_comments') THEN
    ALTER TABLE public.task_comments ENABLE ROW LEVEL SECURITY;
    
    CREATE POLICY "Project stakeholders can manage task comments" 
    ON public.task_comments FOR ALL 
    USING (
      is_admin(auth.uid()) OR 
      is_project_manager(auth.uid()) OR 
      user_id = auth.uid()
    ) 
    WITH CHECK (is_admin(auth.uid()) OR is_project_manager(auth.uid()) OR user_id = auth.uid());
  END IF;
END $$;

DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'project_status_updates') THEN
    ALTER TABLE public.project_status_updates ENABLE ROW LEVEL SECURITY;
    
    CREATE POLICY "Project stakeholders can view project updates" 
    ON public.project_status_updates FOR SELECT 
    USING (
      is_admin(auth.uid()) OR 
      is_project_manager(auth.uid()) OR 
      user_id = auth.uid()
    );

    CREATE POLICY "Project stakeholders can create project updates" 
    ON public.project_status_updates FOR INSERT 
    WITH CHECK (
      is_admin(auth.uid()) OR 
      is_project_manager(auth.uid()) OR 
      user_id = auth.uid()
    );
  END IF;
END $$;