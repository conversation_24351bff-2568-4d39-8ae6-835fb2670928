-- Fix the notification trigger function to handle the correct enum type
CREATE OR <PERSON><PERSON>LACE FUNCTION public.auto_create_notifications()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    -- Task assignment notifications
    IF TG_TABLE_NAME = 'tasks' AND TG_OP = 'UPDATE' AND OLD.assigned_to IS DISTINCT FROM NEW.assigned_to AND NEW.assigned_to IS NOT NULL THEN
        PERFORM create_notification(
            NEW.assigned_to,
            'New Task Assigned',
            'You have been assigned to task "' || NEW.title || '"',
            'info',
            '/projects/' || NEW.project_id
        );
    END IF;
    
    -- Task completion notifications - check against task_status enum values
    IF TG_TABLE_NAME = 'tasks' AND TG_OP = 'UPDATE' AND OLD.status != NEW.status AND NEW.status = 'completed'::task_status THEN
        -- Notify project creator
        PERFOR<PERSON> create_notification(
            (SELECT created_by FROM projects WHERE id = NEW.project_id),
            'Task Completed',
            'Task "' || NEW.title || '" has been completed',
            'success',
            '/projects/' || NEW.project_id
        );
    END IF;
    
    -- Support ticket assignment notifications
    IF TG_TABLE_NAME = 'support_tickets' AND TG_OP = 'UPDATE' AND OLD.assigned_to IS DISTINCT FROM NEW.assigned_to AND NEW.assigned_to IS NOT NULL THEN
        PERFORM create_notification(
            NEW.assigned_to,
            'Support Ticket Assigned',
            'You have been assigned to ticket "' || NEW.title || '"',
            'warning',
            '/support-tickets'
        );
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$function$;