-- Create missing tables and add RLS policies for existing tables
-- This completes the database schema for RatioHub

-- Create task_comments table for task discussions
CREATE TABLE public.task_comments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  task_id UUID NOT NULL,
  user_id UUID NOT NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create project_status_updates table for project activity feed
CREATE TABLE public.project_status_updates (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID NOT NULL,
  user_id UUID NOT NULL,
  update_type TEXT NOT NULL, -- 'status_change', 'task_created', 'milestone_completed', etc.
  title TEXT NOT NULL,
  description TEXT,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on new tables
ALTER TABLE public.task_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_status_updates ENABLE ROW LEVEL SECURITY;

-- Create comprehensive RLS policies for all tables

-- Profiles policies
CREATE POLICY "Strict profile access - own profile or admin only" 
ON public.profiles FOR SELECT 
USING ((auth.uid() = user_id) OR is_admin(auth.uid()));

CREATE POLICY "Users can create only their own profile" 
ON public.profiles FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update only their own profile" 
ON public.profiles FOR UPDATE 
USING (auth.uid() = user_id) 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Only admins can delete profiles" 
ON public.profiles FOR DELETE 
USING (is_admin(auth.uid()));

-- User roles policies
CREATE POLICY "Users can view their own roles" 
ON public.user_roles FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all roles" 
ON public.user_roles FOR ALL 
USING (is_admin(auth.uid())) 
WITH CHECK (is_admin(auth.uid()));

-- Teams policies
CREATE POLICY "Team members can view their teams" 
ON public.teams FOR SELECT 
USING (is_admin(auth.uid()) OR is_project_manager(auth.uid()) OR id IN (
  SELECT team_id FROM team_members WHERE user_id = auth.uid()
));

CREATE POLICY "Admins and project managers can manage teams" 
ON public.teams FOR ALL 
USING (is_admin(auth.uid()) OR is_project_manager(auth.uid())) 
WITH CHECK (is_admin(auth.uid()) OR is_project_manager(auth.uid()));

-- Team members policies
CREATE POLICY "Users can view team memberships" 
ON public.team_members FOR SELECT 
USING (is_admin(auth.uid()) OR is_project_manager(auth.uid()) OR user_id = auth.uid());

CREATE POLICY "Admins and project managers can manage team members" 
ON public.team_members FOR ALL 
USING (is_admin(auth.uid()) OR is_project_manager(auth.uid())) 
WITH CHECK (is_admin(auth.uid()) OR is_project_manager(auth.uid()));

-- Projects policies
CREATE POLICY "Users can view projects they're involved in" 
ON public.projects FOR SELECT 
USING (
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  created_by = auth.uid() OR 
  client_id = auth.uid() OR 
  EXISTS (SELECT 1 FROM team_members tm WHERE tm.team_id = projects.team_id AND tm.user_id = auth.uid())
);

CREATE POLICY "Admins and project managers can manage projects" 
ON public.projects FOR ALL 
USING (is_admin(auth.uid()) OR is_project_manager(auth.uid())) 
WITH CHECK (is_admin(auth.uid()) OR is_project_manager(auth.uid()));

-- Milestones policies
CREATE POLICY "Project stakeholders can manage milestones" 
ON public.milestones FOR ALL 
USING (
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  EXISTS (
    SELECT 1 FROM projects p 
    WHERE p.id = milestones.project_id AND (
      p.created_by = auth.uid() OR 
      EXISTS (SELECT 1 FROM team_members tm WHERE tm.team_id = p.team_id AND tm.user_id = auth.uid())
    )
  )
) 
WITH CHECK (
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  EXISTS (
    SELECT 1 FROM projects p 
    WHERE p.id = milestones.project_id AND (
      p.created_by = auth.uid() OR 
      EXISTS (SELECT 1 FROM team_members tm WHERE tm.team_id = p.team_id AND tm.user_id = auth.uid())
    )
  )
);

-- Tasks policies
CREATE POLICY "Project stakeholders can manage tasks" 
ON public.tasks FOR ALL 
USING (
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  assigned_to = auth.uid() OR 
  created_by = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM projects p 
    WHERE p.id = tasks.project_id AND (
      p.created_by = auth.uid() OR 
      EXISTS (SELECT 1 FROM team_members tm WHERE tm.team_id = p.team_id AND tm.user_id = auth.uid())
    )
  )
) 
WITH CHECK (
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  EXISTS (
    SELECT 1 FROM projects p 
    WHERE p.id = tasks.project_id AND (
      p.created_by = auth.uid() OR 
      EXISTS (SELECT 1 FROM team_members tm WHERE tm.team_id = p.team_id AND tm.user_id = auth.uid())
    )
  )
);

-- Task comments policies
CREATE POLICY "Project stakeholders can manage task comments" 
ON public.task_comments FOR ALL 
USING (
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  user_id = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM tasks t 
    JOIN projects p ON p.id = t.project_id 
    WHERE t.id = task_comments.task_id AND (
      t.assigned_to = auth.uid() OR 
      t.created_by = auth.uid() OR 
      p.created_by = auth.uid() OR 
      EXISTS (SELECT 1 FROM team_members tm WHERE tm.team_id = p.team_id AND tm.user_id = auth.uid())
    )
  )
) 
WITH CHECK (is_admin(auth.uid()) OR is_project_manager(auth.uid()) OR user_id = auth.uid());

-- Support tickets policies
CREATE POLICY "Users can manage their own tickets" 
ON public.support_tickets FOR ALL 
USING ((created_by = auth.uid()) OR (assigned_to = auth.uid())) 
WITH CHECK (created_by = auth.uid());

CREATE POLICY "Admins and project managers can manage all tickets" 
ON public.support_tickets FOR ALL 
USING (is_admin(auth.uid()) OR is_project_manager(auth.uid())) 
WITH CHECK (is_admin(auth.uid()) OR is_project_manager(auth.uid()));

CREATE POLICY "Project stakeholders can view project tickets" 
ON public.support_tickets FOR SELECT 
USING (
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  created_by = auth.uid() OR 
  assigned_to = auth.uid() OR 
  (project_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM projects p 
    WHERE p.id = support_tickets.project_id AND (
      p.created_by = auth.uid() OR 
      EXISTS (SELECT 1 FROM team_members tm WHERE tm.team_id = p.team_id AND tm.user_id = auth.uid())
    )
  ))
);

-- Time entries policies
CREATE POLICY "Users can manage their own time entries" 
ON public.time_entries FOR ALL 
USING (user_id = auth.uid()) 
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Admins and project managers can view all time entries" 
ON public.time_entries FOR SELECT 
USING (
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  user_id = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM projects p 
    WHERE p.id = time_entries.project_id AND (
      p.created_by = auth.uid() OR 
      EXISTS (SELECT 1 FROM team_members tm WHERE tm.team_id = p.team_id AND tm.user_id = auth.uid())
    )
  )
);

-- File attachments policies
CREATE POLICY "Project stakeholders can manage attachments" 
ON public.file_attachments FOR ALL 
USING (
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  uploaded_by = auth.uid() OR 
  (project_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM projects p 
    WHERE p.id = file_attachments.project_id AND (
      p.created_by = auth.uid() OR 
      EXISTS (SELECT 1 FROM team_members tm WHERE tm.team_id = p.team_id AND tm.user_id = auth.uid())
    )
  )) OR 
  (task_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM tasks t 
    JOIN projects p ON p.id = t.project_id 
    WHERE t.id = file_attachments.task_id AND (
      t.assigned_to = auth.uid() OR 
      t.created_by = auth.uid() OR 
      p.created_by = auth.uid() OR 
      EXISTS (SELECT 1 FROM team_members tm WHERE tm.team_id = p.team_id AND tm.user_id = auth.uid())
    )
  ))
) 
WITH CHECK (is_admin(auth.uid()) OR is_project_manager(auth.uid()) OR uploaded_by = auth.uid());

-- Project status updates policies
CREATE POLICY "Project stakeholders can view project updates" 
ON public.project_status_updates FOR SELECT 
USING (
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  user_id = auth.uid() OR 
  EXISTS (
    SELECT 1 FROM projects p 
    WHERE p.id = project_status_updates.project_id AND (
      p.created_by = auth.uid() OR 
      EXISTS (SELECT 1 FROM team_members tm WHERE tm.team_id = p.team_id AND tm.user_id = auth.uid())
    )
  )
);

CREATE POLICY "Project stakeholders can create project updates" 
ON public.project_status_updates FOR INSERT 
WITH CHECK (
  is_admin(auth.uid()) OR 
  is_project_manager(auth.uid()) OR 
  user_id = auth.uid()
);

-- Activity logs policies
CREATE POLICY "Only admins can view activity logs" 
ON public.activity_logs FOR SELECT 
USING (is_admin(auth.uid()));

CREATE POLICY "Admins can view all activity logs" 
ON public.activity_logs FOR SELECT 
USING (is_admin(auth.uid()));

CREATE POLICY "System can insert activity logs" 
ON public.activity_logs FOR INSERT 
WITH CHECK (true);

-- Security events policies
CREATE POLICY "Only admins can view security events" 
ON public.security_events FOR SELECT 
USING (is_admin(auth.uid()));

CREATE POLICY "System can insert security events" 
ON public.security_events FOR INSERT 
WITH CHECK (true);