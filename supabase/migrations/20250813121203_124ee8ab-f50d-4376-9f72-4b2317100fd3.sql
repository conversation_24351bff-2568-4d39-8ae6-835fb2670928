-- Add sample invoices for billing with proper UUIDs
CREATE TABLE IF NOT EXISTS invoices (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    invoice_number TEXT NOT NULL UNIQUE,
    project_id UUID REFERENCES projects(id),
    amount DECIMAL(10,2) NOT NULL,
    status TEXT NOT NULL DEFAULT 'draft',
    due_date DATE,
    issued_date DATE DEFAULT CURRENT_DATE,
    created_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on invoices
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for invoices
CREATE POLICY "Admins and project managers can manage invoices" 
ON invoices FOR ALL 
TO authenticated 
USING (is_admin(auth.uid()) OR is_project_manager(auth.uid()))
WITH CHECK (is_admin(auth.uid()) OR is_project_manager(auth.uid()));

-- Insert sample invoices
INSERT INTO invoices (id, invoice_number, project_id, amount, status, due_date, issued_date, created_by) VALUES
('11111111-aaaa-bbbb-cccc-dddddddddddd'::uuid, 'INV-2024-001', '11111111-1111-1111-1111-111111111111'::uuid, 75000.00, 'sent', '2024-03-15', '2024-02-15', '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid),
('22222222-aaaa-bbbb-cccc-dddddddddddd'::uuid, 'INV-2024-002', '11111111-1111-1111-1111-111111111111'::uuid, 45000.00, 'paid', '2024-04-30', '2024-03-30', '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid),
('33333333-aaaa-bbbb-cccc-dddddddddddd'::uuid, 'INV-2024-003', '*************-2222-2222-************'::uuid, 28000.00, 'draft', '2024-08-31', '2024-07-31', '183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid);

-- Add some time entries for billing calculations using gen_random_uuid()
INSERT INTO time_entries (user_id, project_id, task_id, description, start_time, end_time, duration_minutes, is_billable, hourly_rate) VALUES
('183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid, '11111111-1111-1111-1111-111111111111'::uuid, 'bbbbbbbb-**************-************'::uuid, 'Chart analysis and design work', '2024-04-01 09:00:00+00', '2024-04-01 17:00:00+00', 480, true, 150),
('183d5d61-4b48-4f60-b4a3-1cc45d76e713'::uuid, '*************-2222-2222-************'::uuid, null, 'General project consultation', '2024-03-15 10:00:00+00', '2024-03-15 16:00:00+00', 360, true, 140);

-- Make sure tasks can be created without requiring phases or milestones
-- Make milestone_id nullable (if not already)
DO $$ 
BEGIN
    -- Check if column constraint exists and remove it
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'tasks' AND constraint_type = 'CHECK' 
        AND constraint_name LIKE '%milestone_id%'
    ) THEN
        ALTER TABLE tasks DROP CONSTRAINT tasks_milestone_id_check;
    END IF;
    
    -- Make sure milestone_id is nullable
    ALTER TABLE tasks ALTER COLUMN milestone_id DROP NOT NULL;
EXCEPTION
    WHEN others THEN
        -- Ignore if constraint doesn't exist
        NULL;
END $$;