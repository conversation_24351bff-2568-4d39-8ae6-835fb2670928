-- Simple sample data insertion
-- Insert teams first
INSERT INTO teams (name, description, created_by, is_active) VALUES
('SAP Finance Team', 'Specialized team for SAP Finance implementations', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', true),
('SAP HR Team', 'Expert team for SAP HR and SuccessFactors projects', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', true);

-- Insert team members
INSERT INTO team_members (team_id, user_id, role) 
SELECT id, '183d5d61-4b48-4f60-b4a3-1cc45d76e713', 'lead' 
FROM teams WHERE name IN ('SAP Finance Team', 'SAP HR Team');

-- Update projects with team assignments
UPDATE projects SET team_id = (SELECT id FROM teams WHERE name = 'SAP Finance Team') WHERE project_id = 'SA001';
UPDATE projects SET team_id = (SELECT id FROM teams WHERE name = 'SAP HR Team') WHERE project_id = 'SA002';

-- Insert phases
INSERT INTO phases (project_id, name, description, status, start_date, end_date, order_index, created_by) 
SELECT id, 'Discovery & Planning', 'Requirements gathering and project planning phase', 'completed', '2024-01-15', '2024-02-28', 1, '183d5d61-4b48-4f60-b4a3-1cc45d76e713'
FROM projects WHERE project_id = 'SA001';

INSERT INTO phases (project_id, name, description, status, start_date, end_date, order_index, created_by) 
SELECT id, 'Design & Configuration', 'System design and initial configuration', 'in_progress', '2024-03-01', '2024-05-15', 2, '183d5d61-4b48-4f60-b4a3-1cc45d76e713'
FROM projects WHERE project_id = 'SA001';

-- Insert milestones
INSERT INTO milestones (project_id, phase_id, name, description, due_date, is_completed, completed_by, completed_at) 
SELECT p.id, ph.id, 'Requirements Document Approved', 'Complete business requirements documentation', '2024-02-15', true, '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-02-14'
FROM projects p 
JOIN phases ph ON ph.project_id = p.id 
WHERE p.project_id = 'SA001' AND ph.name = 'Discovery & Planning';

INSERT INTO milestones (project_id, phase_id, name, description, due_date, is_completed) 
SELECT p.id, ph.id, 'System Design Complete', 'Technical design and architecture finalized', '2024-04-15', false
FROM projects p 
JOIN phases ph ON ph.project_id = p.id 
WHERE p.project_id = 'SA001' AND ph.name = 'Design & Configuration';

-- Insert tasks
INSERT INTO tasks (project_id, milestone_id, task_id, title, description, status, priority, assigned_to, created_by, due_date, estimated_hours, actual_hours, is_billable) 
SELECT p.id, m.id, 'SA001-T001', 'Conduct Stakeholder Interviews', 'Interview key stakeholders to gather business requirements', 'completed', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-02-10', 40, 38, true
FROM projects p 
JOIN milestones m ON m.project_id = p.id 
WHERE p.project_id = 'SA001' AND m.name = 'Requirements Document Approved';

INSERT INTO tasks (project_id, milestone_id, task_id, title, description, status, priority, assigned_to, created_by, due_date, estimated_hours, actual_hours, is_billable) 
SELECT p.id, m.id, 'SA001-T002', 'Design Chart of Accounts Structure', 'Create and validate chart of accounts for S/4HANA', 'in_progress', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-04-10', 24, 12, true
FROM projects p 
JOIN milestones m ON m.project_id = p.id 
WHERE p.project_id = 'SA001' AND m.name = 'System Design Complete';

-- Insert subtasks
INSERT INTO tasks (project_id, milestone_id, parent_task_id, task_id, title, description, status, priority, assigned_to, created_by, due_date, estimated_hours, actual_hours, is_billable) 
SELECT t.project_id, t.milestone_id, t.id, 'SA001-T001-S01', 'Prepare Interview Questions', 'Create structured interview questionnaire', 'completed', 'medium', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-02-05', 8, 6, true
FROM tasks t WHERE t.task_id = 'SA001-T001';

-- Insert time entries
INSERT INTO time_entries (user_id, project_id, task_id, description, start_time, end_time, duration_minutes, is_billable, hourly_rate) 
SELECT '183d5d61-4b48-4f60-b4a3-1cc45d76e713', t.project_id, t.id, 'Stakeholder interview session 1', '2024-02-05 09:00:00+00', '2024-02-05 17:00:00+00', 480, true, 150
FROM tasks t WHERE t.task_id = 'SA001-T001';

-- Insert support tickets
INSERT INTO support_tickets (ticket_id, title, description, status, priority, created_by, project_id) 
SELECT 'TKT-001', 'SAP System Performance Issue', 'Slow response time in FI module during peak hours', 'open', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', id
FROM projects WHERE project_id = 'SA001';

INSERT INTO support_tickets (ticket_id, title, description, status, priority, created_by, project_id) 
SELECT 'TKT-002', 'Chart of Accounts Validation', 'Need validation for proposed chart of accounts structure', 'in_progress', 'medium', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', id
FROM projects WHERE project_id = 'SA001';