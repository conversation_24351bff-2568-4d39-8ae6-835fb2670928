-- Insert sample notifications for demo
INSERT INTO public.notifications (user_id, title, message, type, action_url)
SELECT 
  '183d5d61-4b48-4f60-b4a3-1cc45d76e713',
  'New Task Assigned',
  'You have been assigned to task "Configure S/4HANA Chart of Accounts"',
  'info',
  '/projects'
WHERE NOT EXISTS (
  SELECT 1 FROM public.notifications 
  WHERE title = 'New Task Assigned' 
  AND user_id = '183d5d61-4b48-4f60-b4a3-1cc45d76e713'
);

INSERT INTO public.notifications (user_id, title, message, type, action_url)
SELECT 
  '183d5d61-4b48-4f60-b4a3-1cc45d76e713',
  'Project Milestone Completed',
  'The "Infrastructure Setup" milestone has been completed successfully',
  'success',
  '/projects'
WHERE NOT EXISTS (
  SELECT 1 FROM public.notifications 
  WHERE title = 'Project Milestone Completed' 
  AND user_id = '183d5d61-4b48-4f60-b4a3-1cc45d76e713'
);

INSERT INTO public.notifications (user_id, title, message, type, action_url)
SELECT 
  '183d5d61-4b48-4f60-b4a3-1cc45d76e713',
  'Invoice Generated',
  'Invoice INV-2024-001 has been generated for Global Manufacturing S/4HANA Implementation',
  'info',
  '/billing'
WHERE NOT EXISTS (
  SELECT 1 FROM public.notifications 
  WHERE title = 'Invoice Generated' 
  AND user_id = '183d5d61-4b48-4f60-b4a3-1cc45d76e713'
);

-- Create trigger function to auto-create notifications for key events
CREATE OR REPLACE FUNCTION public.auto_create_notifications()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    -- Task assignment notifications
    IF TG_TABLE_NAME = 'tasks' AND TG_OP = 'UPDATE' AND OLD.assigned_to IS DISTINCT FROM NEW.assigned_to AND NEW.assigned_to IS NOT NULL THEN
        PERFORM create_notification(
            NEW.assigned_to,
            'New Task Assigned',
            'You have been assigned to task "' || NEW.title || '"',
            'info',
            '/projects/' || NEW.project_id
        );
    END IF;
    
    -- Task completion notifications
    IF TG_TABLE_NAME = 'tasks' AND TG_OP = 'UPDATE' AND OLD.status != NEW.status AND NEW.status = 'completed' THEN
        -- Notify project owner
        PERFORM create_notification(
            (SELECT created_by FROM projects WHERE id = NEW.project_id),
            'Task Completed',
            'Task "' || NEW.title || '" has been completed',
            'success',
            '/projects/' || NEW.project_id
        );
    END IF;
    
    -- Support ticket assignment notifications
    IF TG_TABLE_NAME = 'support_tickets' AND TG_OP = 'UPDATE' AND OLD.assigned_to IS DISTINCT FROM NEW.assigned_to AND NEW.assigned_to IS NOT NULL THEN
        PERFORM create_notification(
            NEW.assigned_to,
            'Support Ticket Assigned',
            'You have been assigned to ticket "' || NEW.title || '"',
            'warning',
            '/support-tickets'
        );
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$;

-- Create triggers for automatic notifications
DROP TRIGGER IF EXISTS auto_notify_task_changes ON public.tasks;
CREATE TRIGGER auto_notify_task_changes
    AFTER UPDATE ON public.tasks
    FOR EACH ROW
    EXECUTE FUNCTION public.auto_create_notifications();

DROP TRIGGER IF EXISTS auto_notify_ticket_changes ON public.support_tickets;
CREATE TRIGGER auto_notify_ticket_changes
    AFTER UPDATE ON public.support_tickets
    FOR EACH ROW
    EXECUTE FUNCTION public.auto_create_notifications();

-- Create function to auto-update time entries when tasks are completed
CREATE OR REPLACE FUNCTION public.auto_update_task_time()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    total_minutes INTEGER;
BEGIN
    -- When task status changes to completed, calculate actual hours from time entries
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        SELECT COALESCE(SUM(duration_minutes), 0) INTO total_minutes
        FROM time_entries
        WHERE task_id = NEW.id;
        
        -- Update actual hours on the task
        NEW.actual_hours = ROUND((total_minutes::DECIMAL / 60), 2);
    END IF;
    
    RETURN NEW;
END;
$$;

-- Create trigger for auto-updating task hours
DROP TRIGGER IF EXISTS auto_update_task_hours ON public.tasks;
CREATE TRIGGER auto_update_task_hours
    BEFORE UPDATE ON public.tasks
    FOR EACH ROW
    EXECUTE FUNCTION public.auto_update_task_time();