-- Sample data insertion with correct phase status values
INSERT INTO projects (id, name, project_id, description, status, created_by, start_date, end_date, budget, hourly_rate, is_billable, currency) VALUES
('********-1111-1111-1111-************', 'SAP S/4HANA Finance Implementation', 'SA001', 'Complete SAP S/4HANA Finance module implementation for enterprise client', 'active', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-01-15', '2024-08-15', 500000, 150, true, 'USD'),
('********-2222-2222-2222-************', 'SAP SuccessFactors HR Transformation', 'SA002', 'SAP SuccessFactors implementation for global HR processes', 'active', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-02-01', '2024-09-30', 350000, 140, true, 'USD'),
('33333333-3333-3333-3333-************', 'SAP Ariba Procurement Platform', 'SA003', 'SAP Ariba implementation for procurement and sourcing optimization', 'planning', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-04-01', '2024-12-15', 275000, 135, true, 'USD');

-- Insert sample teams
INSERT INTO teams (id, name, description, created_by, is_active) VALUES
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'SAP Finance Team', 'Specialized team for SAP Finance implementations', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', true),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'SAP HR Team', 'Expert team for SAP HR and SuccessFactors projects', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', true);

-- Update projects with team assignments  
UPDATE projects SET team_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa' WHERE id = '********-1111-1111-1111-************';
UPDATE projects SET team_id = 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb' WHERE id = '********-2222-2222-2222-************';

-- Add user to teams
INSERT INTO team_members (team_id, user_id, role) VALUES
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', 'lead'),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', 'lead');

-- Insert phases with correct status values: planning, in_progress, completed, on_hold
INSERT INTO phases (id, project_id, name, description, status, start_date, end_date, order_index, created_by) VALUES
('f1111111-1111-1111-1111-************', '********-1111-1111-1111-************', 'Discovery & Planning', 'Requirements gathering and project planning phase', 'completed', '2024-01-15', '2024-02-28', 1, '183d5d61-4b48-4f60-b4a3-1cc45d76e713'),
('f2222222-2222-2222-2222-************', '********-1111-1111-1111-************', 'Design & Configuration', 'System design and initial configuration', 'in_progress', '2024-03-01', '2024-05-15', 2, '183d5d61-4b48-4f60-b4a3-1cc45d76e713'),
('f3333333-3333-3333-3333-************', '********-1111-1111-1111-************', 'Development & Testing', 'Custom development and system testing', 'planning', '2024-05-16', '2024-07-15', 3, '183d5d61-4b48-4f60-b4a3-1cc45d76e713');

-- Insert milestones
INSERT INTO milestones (id, project_id, phase_id, name, description, due_date, is_completed, completed_by, completed_at) VALUES
('m1111111-1111-1111-1111-************', '********-1111-1111-1111-************', 'f1111111-1111-1111-1111-************', 'Requirements Document Approved', 'Complete business requirements documentation', '2024-02-15', true, '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-02-14'),
('m2222222-2222-2222-2222-************', '********-1111-1111-1111-************', 'f2222222-2222-2222-2222-************', 'System Design Complete', 'Technical design and architecture finalized', '2024-04-15', false, null, null),
('m3333333-3333-3333-3333-************', '********-1111-1111-1111-************', 'f3333333-3333-3333-3333-************', 'Development Phase Ready', 'Development environment setup complete', '2024-06-01', false, null, null);

-- Insert tasks with various statuses
INSERT INTO tasks (id, project_id, milestone_id, task_id, title, description, status, priority, assigned_to, created_by, due_date, estimated_hours, actual_hours, is_billable) VALUES
('t1111111-1111-1111-1111-************', '********-1111-1111-1111-************', 'm1111111-1111-1111-1111-************', 'SA001-T001', 'Conduct Stakeholder Interviews', 'Interview key stakeholders to gather business requirements', 'completed', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-02-10', 40, 38, true),
('t2222222-2222-2222-2222-************', '********-1111-1111-1111-************', 'm2222222-2222-2222-2222-************', 'SA001-T002', 'Design Chart of Accounts Structure', 'Create and validate chart of accounts for S/4HANA', 'in_progress', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-04-10', 24, 12, true),
('t3333333-3333-3333-3333-************', '********-1111-1111-1111-************', 'm2222222-2222-2222-2222-************', 'SA001-T003', 'Configure Financial Accounting Module', 'Set up basic FI configuration in SAP S/4HANA', 'todo', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-04-20', 56, 0, true),
('t4444444-4444-4444-4444-************', '********-1111-1111-1111-************', 'm3333333-3333-3333-3333-************', 'SA001-T004', 'Setup Development Environment', 'Configure development servers and tools', 'review', 'medium', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-05-30', 16, 14, true),
('t5555555-5555-5555-5555-************', '********-1111-1111-1111-************', 'm3333333-3333-3333-3333-************', 'SA001-T005', 'Create Unit Tests', 'Develop comprehensive unit test suite', 'blocked', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-06-15', 32, 0, true);

-- Insert subtasks
INSERT INTO tasks (id, project_id, milestone_id, parent_task_id, task_id, title, description, status, priority, assigned_to, created_by, due_date, estimated_hours, actual_hours, is_billable) VALUES
('s1111111-1111-1111-1111-************', '********-1111-1111-1111-************', 'm1111111-1111-1111-1111-************', 't1111111-1111-1111-1111-************', 'SA001-T001-S01', 'Prepare Interview Questions', 'Create structured interview questionnaire', 'completed', 'medium', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-02-05', 8, 6, true),
('s2222222-2222-2222-2222-************', '********-1111-1111-1111-************', 'm1111111-1111-1111-1111-************', 't1111111-1111-1111-1111-************', 'SA001-T001-S02', 'Schedule Stakeholder Meetings', 'Coordinate and schedule all stakeholder interviews', 'completed', 'low', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-02-07', 4, 3, true),
('s3333333-3333-3333-3333-************', '********-1111-1111-1111-************', 'm2222222-2222-2222-2222-************', 't2222222-2222-2222-2222-************', 'SA001-T002-S01', 'Analyze Current Chart of Accounts', 'Review existing chart of accounts structure', 'completed', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-03-25', 12, 10, true),
('s4444444-4444-4444-4444-************', '********-1111-1111-1111-************', 'm2222222-2222-2222-2222-************', 't2222222-2222-2222-2222-************', 'SA001-T002-S02', 'Design New Chart Structure', 'Create optimized chart of accounts for S/4HANA', 'in_progress', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '2024-04-05', 12, 2, true);

-- Insert time entries
INSERT INTO time_entries (id, user_id, project_id, task_id, description, start_time, end_time, duration_minutes, is_billable, hourly_rate) VALUES
('te111111-1111-1111-1111-************', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '********-1111-1111-1111-************', 't1111111-1111-1111-1111-************', 'Stakeholder interview session 1', '2024-02-05 09:00:00+00', '2024-02-05 17:00:00+00', 480, true, 150),
('te222222-2222-2222-2222-************', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '********-1111-1111-1111-************', 't1111111-1111-1111-1111-************', 'Stakeholder interview session 2', '2024-02-06 09:00:00+00', '2024-02-06 17:00:00+00', 480, true, 150),
('te333333-3333-3333-3333-************', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '********-1111-1111-1111-************', 't2222222-2222-2222-2222-************', 'Chart design work', '2024-03-25 10:00:00+00', '2024-03-25 16:00:00+00', 360, true, 150),
('te444444-4444-4444-4444-************', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '********-1111-1111-1111-************', 't4444444-4444-4444-4444-************', 'Development environment setup', '2024-05-20 08:00:00+00', '2024-05-20 18:00:00+00', 600, true, 150);

-- Insert support tickets
INSERT INTO support_tickets (id, ticket_id, title, description, status, priority, created_by, project_id) VALUES
('st111111-1111-1111-1111-************', 'TKT-001', 'SAP System Performance Issue', 'Slow response time in FI module during peak hours', 'open', 'high', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '********-1111-1111-1111-************'),
('st222222-2222-2222-2222-************', 'TKT-002', 'Chart of Accounts Validation', 'Need validation for proposed chart of accounts structure', 'in_progress', 'medium', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '********-1111-1111-1111-************'),
('st333333-3333-3333-3333-************', 'TKT-003', 'User Access Request', 'Request for additional SAP module access permissions', 'resolved', 'low', '183d5d61-4b48-4f60-b4a3-1cc45d76e713', '********-2222-2222-2222-************');